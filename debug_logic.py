#!/usr/bin/env python3
"""
🔍 DEBUG SCRIPT - Test GPU/CPU Logic
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_detection():
    """Test system capabilities detection"""
    print("🔍 TESTING SYSTEM DETECTION...")
    
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor()
        
        # Test GPU detection
        has_gpu, gpu_types = processor.check_gpu_support()
        print(f"📊 GPU Detection: {has_gpu}")
        print(f"📊 GPU Types: {gpu_types}")
        
        # Test optimal threads
        optimal_threads = processor.get_optimal_threads()
        print(f"📊 Optimal Threads: {optimal_threads}")
        
        # Test encoder selection
        encoder, gpu_type = processor.get_optimal_encoder()
        print(f"📊 Best Encoder: {encoder} ({gpu_type})")
        
        return {
            'has_gpu': has_gpu,
            'gpu_types': gpu_types,
            'optimal_threads': optimal_threads,
            'encoder': encoder,
            'gpu_type': gpu_type
        }
        
    except Exception as e:
        print(f"❌ System detection error: {e}")
        return None

def test_ui_logic(video_count):
    """Test UI logic for different video counts"""
    print(f"\n🎯 TESTING UI LOGIC FOR {video_count} VIDEOS...")
    
    # Simulate system info
    system_info = {
        'has_gpu': True,
        'gpu_types': ['nvidia'],
        'optimal_threads': 8
    }
    
    # Test logic from enhanced_ui_complete.py
    if video_count <= 3:
        # 1-3 videos: Always use GPU sequential processing (no multi-threading)
        use_gpu = system_info['has_gpu']
        num_threads = 1  # Sequential GPU processing
        if video_count == 1:
            processing_mode = "🚀 GPU (Single Video)" if use_gpu else "💻 CPU (No GPU)"
        else:
            processing_mode = f"🚀 GPU Sequential ({video_count} videos)" if use_gpu else f"💻 CPU Sequential ({video_count} videos)"
    else:
        # 4+ videos: Use multi-threading
        if system_info['has_gpu']:
            # GPU available: Use GPU + CPU multi-threading
            use_gpu = True
            num_threads = min(system_info['optimal_threads'], video_count, 4)  # Multi-threading
            processing_mode = f"🔥 GPU + CPU ({num_threads} Threads Multi-Processing)"
        else:
            # No GPU: CPU only multi-threading
            use_gpu = False
            num_threads = min(system_info['optimal_threads'], video_count)
            processing_mode = f"💻 CPU Only ({num_threads} Threads - No GPU)"
    
    print(f"📊 Video Count: {video_count}")
    print(f"📊 Use GPU: {use_gpu}")
    print(f"📊 Num Threads: {num_threads}")
    print(f"📊 Processing Mode: {processing_mode}")
    
    return {
        'video_count': video_count,
        'use_gpu': use_gpu,
        'num_threads': num_threads,
        'processing_mode': processing_mode
    }

def test_processor_logic(num_workers):
    """Test video processor logic"""
    print(f"\n⚙️ TESTING PROCESSOR LOGIC WITH {num_workers} WORKERS...")
    
    if num_workers == 1:
        processing_type = "🚀 Sequential GPU Processing (Optimal for 1-3 videos)"
        uses_threadpool = False
    else:
        processing_type = f"🔥 Multi-Threading Processing với {num_workers} workers"
        uses_threadpool = True
    
    print(f"📊 Processing Type: {processing_type}")
    print(f"📊 Uses ThreadPoolExecutor: {uses_threadpool}")
    
    return {
        'num_workers': num_workers,
        'processing_type': processing_type,
        'uses_threadpool': uses_threadpool
    }

def main():
    """Main debug function"""
    print("🚀 STARTING COMPREHENSIVE DEBUG...")
    print("=" * 60)
    
    # 1. Test system detection
    system_info = test_system_detection()
    
    # 2. Test UI logic for different video counts
    test_cases = [1, 2, 3, 4, 5, 8]
    ui_results = []
    
    for count in test_cases:
        result = test_ui_logic(count)
        ui_results.append(result)
    
    # 3. Test processor logic
    processor_results = []
    for result in ui_results:
        proc_result = test_processor_logic(result['num_threads'])
        processor_results.append(proc_result)
    
    # 4. Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY REPORT:")
    print("=" * 60)
    
    if system_info:
        print(f"🖥️ System: GPU={system_info['has_gpu']}, Threads={system_info['optimal_threads']}")
    
    print("\n📊 Logic Test Results:")
    for i, (ui, proc) in enumerate(zip(ui_results, processor_results)):
        print(f"  {ui['video_count']} videos → Threads={ui['num_threads']} → {proc['processing_type']}")
        if ui['video_count'] == 2:
            print(f"    ⚠️ CRITICAL: 2 videos should be Sequential (threads=1)")
            if ui['num_threads'] == 1 and not proc['uses_threadpool']:
                print(f"    ✅ CORRECT: Sequential processing")
            else:
                print(f"    ❌ WRONG: Multi-threading detected!")
    
    print("\n🎯 EXPECTED BEHAVIOR:")
    print("  1-3 videos: Sequential (threads=1, no ThreadPoolExecutor)")
    print("  4+ videos: Multi-threading (threads>1, with ThreadPoolExecutor)")

if __name__ == "__main__":
    main()
