@echo off
chcp 65001 >nul
echo.
echo ============================================================================
echo ⚡ QUICK SETUP - Enhanced TikTok Bypass Tool
echo ============================================================================
echo.

:: Quick check and install
echo 🔍 Quick system check...

:: Check Python
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Python found
    python --version
) else (
    echo ❌ Python not found! Please run setup_after_windows_install.bat first
    pause
    exit /b 1
)

:: Install requirements quickly
echo.
echo 📦 Installing requirements (quick mode)...
python -m pip install --upgrade pip
python -m pip install PyQt5 opencv-python numpy Pillow psutil tqdm requests

:: Test launch
echo.
echo 🧪 Testing tool...
python -c "
try:
    import PyQt5, cv2, numpy, PIL
    print('✅ All core modules OK')
except Exception as e:
    print(f'❌ Import error: {e}')
"

echo.
echo ✅ Quick setup completed!
echo 🚀 Run: python launch_complete.py
echo.
pause
