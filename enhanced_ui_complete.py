#!/usr/bin/env python3
"""
Enhanced UI Complete - Tất cả tính năng cũ + mới với theme sáng
Dựa trên ui_config.py gốc nhưng với theme sáng và thêm AI features
"""

import sys
import os
import time
import warnings

# Suppress Qt warnings and deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*sipPyTypeDict.*")
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Import từ UI cũ
try:
    from ui_config import DragDropFileList, VideoProcessorThread
except ImportError:
    # Fallback: Create simple implementations
    class DragDropFileList(QListWidget):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setAcceptDrops(True)
            self.parent_window = parent

        def dragEnterEvent(self, event):
            if event.mimeData().hasUrls():
                event.accept()
            else:
                event.ignore()

        def dropEvent(self, event):
            files = [u.toLocalFile() for u in event.mimeData().urls()]
            for file_path in files:
                if file_path.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv')):
                    self.addItem(file_path)
                    if self.parent_window:
                        self.parent_window.add_video_file(file_path)

        def add_video_file(self, file_path):
            self.addItem(file_path)

        def clear_files(self):
            self.clear()

    class VideoProcessorThread(QThread):
        progress_updated = pyqtSignal(int)
        status_updated = pyqtSignal(str)
        finished = pyqtSignal()
        error_occurred = pyqtSignal(str)

        def __init__(self, video_paths, effects, zoom_percent, num_threads):
            super().__init__()
            self.video_paths = video_paths
            self.effects = effects
            self.zoom_percent = zoom_percent
            self.num_threads = num_threads

        def run(self):
            try:
                # 🚀 REAL VIDEO PROCESSING - NO MORE FAKE!
                from video_processor import process_videos

                def progress_callback(percent):
                    self.progress_updated.emit(percent)

                def status_callback(message):
                    self.status_updated.emit(message)

                def on_complete():
                    self.finished.emit()

                # 🎯 CALL REAL PROCESSING WITH CORRECT num_threads
                process_videos(
                    self.video_paths,
                    self.effects,
                    progress_callback,
                    on_complete,
                    self.zoom_percent,
                    status_callback,
                    self.num_threads  # ← PASS CORRECT THREADS!
                )

            except Exception as e:
                self.error_occurred.emit(f"Lỗi xử lý: {str(e)}")

# AI Smart Enhancer removed - was not useful

class EnhancedCompleteUI(QWidget):
    def __init__(self):
        super().__init__()
        
        # Initialize processors
        self.video_paths = []
        self.processor_thread = None
        self.start_time = None
        self.processed_count = 0
        self.total_count = 0
        
        # AI Smart Enhancer removed
        
        # Setup beautiful fonts and styling
        self.setup_fonts()

        # Setup UI
        self.init_ui()

    def setup_fonts(self):
        """Setup beautiful fonts for the application"""
        from PyQt5.QtGui import QFont, QFontDatabase
        import sys

        # Fix font paths for EXE
        if getattr(sys, 'frozen', False):
            # Running as EXE
            exe_dir = os.path.dirname(sys.executable)
            font_base_paths = [
                os.path.join(exe_dir, "fonts"),
                os.path.join(exe_dir, "_internal", "fonts"),
                os.path.join(exe_dir, "resources", "fonts")
            ]
        else:
            # Running as Python script
            font_base_paths = ["fonts", "resources/fonts"]

        # Add custom fonts if available
        font_files = [
            "Inter-Regular.ttf",
            "Inter-Medium.ttf",
            "Inter-SemiBold.ttf",
            "JetBrainsMono-Regular.ttf"
        ]

        for base_path in font_base_paths:
            for font_file in font_files:
                font_path = os.path.join(base_path, font_file)
                if os.path.exists(font_path):
                    QFontDatabase.addApplicationFont(font_path)
                    break

        # Set application-wide font
        app_font = QFont()

        # Try to use Inter font (modern and beautiful)
        if "Inter" in QFontDatabase().families():
            app_font.setFamily("Inter")
        # Fallback to system fonts
        elif "Segoe UI" in QFontDatabase().families():
            app_font.setFamily("Segoe UI")
        elif "SF Pro Display" in QFontDatabase().families():  # macOS
            app_font.setFamily("SF Pro Display")
        else:
            app_font.setFamily("Arial")

        app_font.setPointSize(10)
        app_font.setWeight(QFont.Normal)

        # Apply to application
        from PyQt5.QtWidgets import QApplication
        QApplication.instance().setFont(app_font)

    # Removed fake system detection function
        
    def init_ui(self):
        """Khởi tạo giao diện hoàn chỉnh"""
        self.setWindowTitle("🎬 Edit Short / Tu Anh Tran")
        self.setGeometry(100, 100, 1100, 1300)  # Cao hơn nhiều để không cần scroll
        self.setMinimumSize(950, 850)

        # Apply light theme
        self.apply_light_theme()

        # Don't auto maximize - keep normal window size
        
        # Main layout với splitter - FINAL BALANCED
        main_layout = QHBoxLayout(self)

        # Left panel - Effects only (60%)
        left_panel = self.create_effects_panel()

        # Right panel - File + Controls + Logs (40%)
        right_panel = self.create_complete_right_panel()

        # Splitter
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 600])  # 50% : 50% - Cân bằng hoàn hảo

        main_layout.addWidget(splitter)
    
    def apply_light_theme(self):
        """Áp dụng theme sáng đẹp với fonts hiện đại"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                color: #212529;
                font-family: 'Inter', 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
                font-size: 13px;
                font-weight: 400;
                letter-spacing: -0.01em;
            }
            
            QGroupBox {
                font-weight: 600;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: #ffffff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 12px 0 12px;
                color: #0d6efd;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: -0.02em;
            }
            
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                letter-spacing: -0.01em;
                transition: all 0.2s ease;
            }
            
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            
            QPushButton:pressed {
                background-color: #0a58ca;
            }
            
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
            
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px 12px;
                background-color: white;
                color: #212529;
            }
            
            QComboBox:hover {
                border-color: #86b7fe;
            }
            
            QCheckBox {
                color: #212529;
                font-weight: 500;
                spacing: 8px;
            }
            
            QSlider::groove:horizontal {
                border: 1px solid #ced4da;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
            }
            
            QSlider::handle:horizontal {
                background: #0d6efd;
                border: 2px solid #0d6efd;
                width: 18px;
                margin: -7px 0;
                border-radius: 9px;
            }
            
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                color: #198754;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                border-radius: 8px;
                padding: 10px;
            }
            
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                text-align: center;
                color: #212529;
                background-color: #f8f9fa;
            }
            
            QProgressBar::chunk {
                background-color: #198754;
                border-radius: 3px;
            }
        """)
    
    def create_effects_panel(self):
        """Tạo panel effects bên trái - CHỈ EFFECTS"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Header
        header = QLabel("🎭 Hiệu Ứng Video & Cài Đặt")
        header.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 700;
                color: #0d6efd;
                padding: 16px;
                border-bottom: 3px solid #0d6efd;
                margin-bottom: 16px;
                background-color: white;
                border-radius: 12px;
                letter-spacing: -0.02em;
                box-shadow: 0 2px 8px rgba(13,110,253,0.1);
            }
        """)
        layout.addWidget(header)

        # Scroll area cho effects - FIX VISIBILITY
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setMinimumHeight(400)  # Đảm bảo có chiều cao tối thiểu

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 1. BYPASS EFFECTS
        bypass_section = self.create_bypass_effects_section()
        scroll_layout.addWidget(bypass_section)
        print("✅ Added Bypass Effects section")

        # 2. GRID EFFECTS
        grid_section = self.create_grid_section()
        scroll_layout.addWidget(grid_section)
        print("✅ Added Grid Effects section")

        # 3. ADVANCED EFFECTS (AI đã chuyển sang bên phải)
        advanced_section = self.create_advanced_effects_section()
        scroll_layout.addWidget(advanced_section)
        print("✅ Added Advanced Effects section")

        scroll_layout.addStretch()
        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        return panel

    def create_complete_right_panel(self):
        """Tạo panel bên phải hoàn chỉnh - FILE + CONTROLS + LOGS"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Header
        header = QLabel("🎬 Edit Short / Tu Anh Tran")
        header.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #0d6efd;
                letter-spacing: -0.02em;
                padding: 12px;
                border-bottom: 3px solid #0d6efd;
                margin-bottom: 12px;
                background-color: white;
                border-radius: 8px;
            }
        """)
        layout.addWidget(header)

        # 1. FILE SELECTION - CHUYỂN TỪ TRÊN XUỐNG
        layout.addWidget(self.create_file_section())

        # AI section removed

        # Preview section removed - keep it simple

        # 4. PLATFORM EXPORT
        layout.addWidget(self.create_export_section())

        # 5. PROCESSING SETTINGS
        layout.addWidget(self.create_processing_section())

        # 4. ACTION BUTTONS
        layout.addWidget(self.create_action_buttons())

        # 5. PROGRESS SECTION
        progress_group = QGroupBox("📊 Tiến Độ Xử Lý")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        progress_layout.addWidget(self.progress_bar)

        self.label_progress = QLabel("Sẵn sàng xử lý")
        self.label_progress.setStyleSheet("font-size: 12px; color: #6c757d;")
        progress_layout.addWidget(self.label_progress)

        layout.addWidget(progress_group)

        # 6. LOG DISPLAY - COMPACT
        log_group = QGroupBox("📋 Nhật Ký Xử Lý")
        log_layout = QVBoxLayout(log_group)

        self.text_log = QTextEdit()
        self.text_log.setReadOnly(True)
        self.text_log.setMinimumHeight(500)  # TĂNG CHIỀU CAO TỐI THIỂU
        self.text_log.setMaximumHeight(700)  # TĂNG CHIỀU CAO LOG CỰC NHIỀU!
        self.text_log.setStyleSheet("""
            QTextEdit {
                font-size: 12px;
                line-height: 1.3;
                background-color: #1e1e1e;
                color: #00ff00;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: 500;
                padding: 8px;
                border: 1px solid #333;
                border-radius: 6px;
            }
        """)
        log_layout.addWidget(self.text_log)

        # Log controls - COMPACT
        log_controls = QHBoxLayout()
        clear_logs_btn = QPushButton("🗑️")
        clear_logs_btn.setMaximumWidth(30)
        clear_logs_btn.setToolTip("Xóa Logs")
        clear_logs_btn.clicked.connect(self.clear_logs)

        save_logs_btn = QPushButton("💾")
        save_logs_btn.setMaximumWidth(30)
        save_logs_btn.setToolTip("Lưu Logs")
        save_logs_btn.clicked.connect(self.save_logs)

        expand_logs_btn = QPushButton("🔍")
        expand_logs_btn.setMaximumWidth(30)
        expand_logs_btn.setToolTip("Mở Rộng Logs")
        expand_logs_btn.clicked.connect(self.expand_logs)

        log_controls.addWidget(clear_logs_btn)
        log_controls.addWidget(save_logs_btn)
        log_controls.addWidget(expand_logs_btn)
        log_controls.addStretch()
        log_layout.addLayout(log_controls)

        layout.addWidget(log_group)
        layout.addStretch()  # Push everything to top

        return panel
    
    def create_file_section(self):
        """File selection - COMPACT cho bên phải"""
        file_group = QGroupBox("📁 Tệp Video")
        file_layout = QVBoxLayout(file_group)

        # Drag & drop list - COMPACT
        self.file_list = DragDropFileList(self)
        self.file_list.setMinimumHeight(80)  # Thấp hơn để fit
        self.file_list.setMaximumHeight(100)  # Giới hạn chiều cao
        file_layout.addWidget(self.file_list)

        # Controls row - COMPACT
        controls_layout = QHBoxLayout()

        # Add files button
        self.btn_add_files = QPushButton("📂")
        self.btn_add_files.setMaximumWidth(40)
        self.btn_add_files.setToolTip("Thêm Tệp")
        self.btn_add_files.clicked.connect(self.select_files)
        self.btn_add_files.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)

        # Clear button
        self.btn_clear = QPushButton("🗑️")
        self.btn_clear.setMaximumWidth(40)
        self.btn_clear.setToolTip("Xóa Tất Cả")
        self.btn_clear.clicked.connect(self.clear_files)
        self.btn_clear.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        controls_layout.addWidget(self.btn_add_files)
        controls_layout.addWidget(self.btn_clear)
        controls_layout.addStretch()
        file_layout.addLayout(controls_layout)

        # Status label - COMPACT
        self.label_selected = QLabel("🖱️ Kéo thả hoặc click 📂")
        self.label_selected.setWordWrap(True)
        self.label_selected.setStyleSheet("""
            color: #6c757d;
            font-style: italic;
            font-size: 11px;
            padding: 3px;
        """)
        file_layout.addWidget(self.label_selected)

        return file_group
    
    def create_bypass_effects_section(self):
        """🔥 ADVANCED BYPASS TECHNIQUES"""
        effects_group = QGroupBox("🔥 Advanced Bypass Techniques")
        effects_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #fdf2f2;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #e74c3c;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        effects_layout = QVBoxLayout(effects_group)
        
        # 🔥 ADVANCED BYPASS CHECKBOXES
        self.checkbox_audio = QCheckBox("🎵 Audio Bypass")
        self.checkbox_audio.setToolTip("Dynamic pitch, tempo, reverb, frequency filtering")

        self.checkbox_color_shift = QCheckBox("🌈 Visual Bypass")
        self.checkbox_color_shift.setToolTip("Color space, hue shift, temperature, anti-AI noise")

        self.checkbox_geometric = QCheckBox("📐 Geometric Bypass")
        self.checkbox_geometric.setToolTip("Micro rotation, scaling, perspective, lens distortion")
        # BỎ AUTO-TICK - để user tự chọn

        self.checkbox_temporal = QCheckBox("⏱️ Temporal Bypass")
        self.checkbox_temporal.setToolTip("FPS variation, temporal noise, speed micro-changes")
        self.checkbox_temporal.setChecked(False)  # Bỏ tick mặc định

        self.checkbox_metadata = QCheckBox("🔍 Metadata Bypass")
        self.checkbox_metadata.setToolTip("Dynamic encoding, random metadata, fingerprint alteration")
        self.checkbox_metadata.setChecked(False)  # Bỏ tick mặc định

        # Basic effects - BỎ DUPLICATE, chỉ tạo 1 lần
        self.checkbox_blur = QCheckBox("🌫️ Blur")
        self.checkbox_shift = QCheckBox("🔄 Shift")
        # Noise và Vibration sẽ tạo ở dưới để tránh duplicate

        # 🌟 WINK-STYLE ENHANCEMENT (NO AUTO-TICK)
        self.checkbox_wink_enhance = QCheckBox("🌟 Wink Enhancement")
        # BỎ AUTO-TICK - để user tự chọn

        # Zoom control (slider)
        zoom_layout = QVBoxLayout()
        zoom_label_layout = QHBoxLayout()
        zoom_label_layout.addWidget(QLabel("🔍 Zoom:"))
        self.label_zoom_value = QLabel("110%")
        zoom_label_layout.addWidget(self.label_zoom_value)
        zoom_layout.addLayout(zoom_label_layout)

        self.slider_zoom = QSlider(Qt.Horizontal)
        self.slider_zoom.setMinimum(100)
        self.slider_zoom.setMaximum(130)
        self.slider_zoom.setValue(110)
        self.slider_zoom.valueChanged.connect(lambda v: self.label_zoom_value.setText(f"{v}%"))
        zoom_layout.addWidget(self.slider_zoom)
        effects_layout.addLayout(zoom_layout)

        # 🔥 ADVANCED BYPASS - 2 CỘT DỌC THEO ẢNH!

        # Tạo layout 2 cột
        bypass_columns = QHBoxLayout()

        # Cột TRÁI
        left_column = QVBoxLayout()
        left_column.addWidget(self.checkbox_audio)
        left_column.addWidget(self.checkbox_color_shift)  # Visual Bypass
        left_column.addWidget(self.checkbox_geometric)
        left_column.addWidget(self.checkbox_blur)
        left_column.addWidget(self.checkbox_shift)
        # Tạo Noise checkbox (KHÔNG DUPLICATE)
        self.checkbox_noise = QCheckBox("🔊 Noise")
        left_column.addWidget(self.checkbox_noise)

        # Cột PHẢI
        right_column = QVBoxLayout()
        # Tạo Temporal Bypass (KHÔNG DUPLICATE)
        self.checkbox_temporal = QCheckBox("⏰ Temporal Bypass")
        right_column.addWidget(self.checkbox_temporal)
        right_column.addWidget(self.checkbox_metadata)
        right_column.addWidget(self.checkbox_wink_enhance)
        # Tạo Vibration (KHÔNG DUPLICATE)
        self.checkbox_vibration = QCheckBox("📳 Vibration")
        right_column.addWidget(self.checkbox_vibration)
        # Tạo Overlay và Frame đen (KHÔNG DUPLICATE)
        self.checkbox_overlay = QCheckBox("🎭 Overlay")
        self.checkbox_frame_black = QCheckBox("⚫ Frame đen")
        right_column.addWidget(self.checkbox_overlay)
        right_column.addWidget(self.checkbox_frame_black)

        # Thêm 2 cột vào layout chính
        bypass_columns.addLayout(left_column)
        bypass_columns.addLayout(right_column)
        effects_layout.addLayout(bypass_columns)

        return effects_group

    def create_grid_section(self):
        """Grid effects từ UI cũ - đầy đủ"""
        grid_group = QGroupBox("📏 Lưới Grid (Chống Reup)")
        grid_layout = QVBoxLayout(grid_group)

        # Grid Type selector
        grid_type_layout = QHBoxLayout()
        grid_type_layout.addWidget(QLabel("Loại Grid:"))
        self.comboBox_grid_type = QComboBox()
        self.comboBox_grid_type.addItems([
            "Không (Tắt)", "3x3 (Quy Tắc 1/3)", "4x4 Grid", "5x5 Grid", "6x6 Grid",
            "Thập Tự Giữa", "Tỷ Lệ Vàng", "Đường Chéo",
            "🔥 Grid Ngẫu Nhiên (Chống Reup)", "🎯 Grid Động (Di Chuyển)",
            "⚡ Grid Bất Đối Xứng", "🌟 Xoắn Fibonacci", "💫 Grid Lục Giác",
            "🎨 Mẫu Tùy Chỉnh", "🔀 Grid Đa Lớp",
            # NEW PATTERNS - COMPREHENSIVE COLLECTION
            "🔴 Dots Pattern (Chấm Tròn)", "🔺 Triangle Pattern (Tam Giác)",
            "⭐ Star Pattern (Ngôi Sao)", "🌊 Wave Pattern (Sóng)",
            "💎 Diamond Pattern (Kim Cương)", "🔶 Rhombus Pattern (Thoi)",
            "🌀 Spiral Pattern (Xoắn Ốc)", "⚡ Zigzag Pattern (Răng Cưa)",
            "🎯 Target Pattern (Bia)", "🕸️ Web Pattern (Mạng Nhện)",
            "🔳 Border Frame (Khung Viền)", "📐 Geometric Mix (Hình Học)",
            "🎪 Circus Pattern (Xiếc)", "🌈 Rainbow Arch (Cầu Vồng)",
            "❄️ Snowflake Pattern (Bông Tuyết)", "🌸 Flower Pattern (Hoa)",
            "⚙️ Gear Pattern (Bánh Răng)", "🔥 Fire Pattern (Lửa)"
        ])
        self.comboBox_grid_type.setCurrentIndex(1)  # Mặc định "3x3 (Quy Tắc 1/3)"
        # KHÔNG connect toggle để grid luôn hiển thị
        # self.comboBox_grid_type.currentTextChanged.connect(self.toggle_grid_options)
        grid_type_layout.addWidget(self.comboBox_grid_type)
        grid_layout.addLayout(grid_type_layout)

        # Grid options frame
        self.grid_options_frame = QFrame()
        grid_options_layout = QVBoxLayout(self.grid_options_frame)

        # Color selection
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("Màu Grid:"))
        self.comboBox_grid_color = QComboBox()
        self.comboBox_grid_color.addItems([
            "Trắng (White)", "Đỏ (Red)", "Xanh Dương (Blue)", "Xanh Lá (Green)",
            "Vàng (Yellow)", "Tím (Purple)", "Cam (Orange)", "Hồng (Pink)",
            "🌈 Rainbow (Multi-Color)", "🔥 Random Color", "💫 Gradient"
        ])
        color_layout.addWidget(self.comboBox_grid_color)
        grid_options_layout.addLayout(color_layout)

        # Opacity slider
        opacity_layout = QVBoxLayout()
        opacity_label_layout = QHBoxLayout()
        opacity_label_layout.addWidget(QLabel("Độ trong suốt:"))
        self.label_opacity_value = QLabel("60%")  # Updated to match new default
        opacity_label_layout.addWidget(self.label_opacity_value)
        opacity_layout.addLayout(opacity_label_layout)

        self.slider_opacity = QSlider(Qt.Horizontal)
        self.slider_opacity.setMinimum(10)
        self.slider_opacity.setMaximum(80)
        self.slider_opacity.setValue(60)  # Increased default opacity for better visibility
        self.slider_opacity.valueChanged.connect(self.update_opacity_label)
        opacity_layout.addWidget(self.slider_opacity)
        grid_options_layout.addLayout(opacity_layout)

        # Line width
        width_layout = QHBoxLayout()
        width_layout.addWidget(QLabel("Độ dày line:"))
        self.spinBox_line_width = QSpinBox()
        self.spinBox_line_width.setMinimum(1)
        self.spinBox_line_width.setMaximum(6)
        self.spinBox_line_width.setValue(2)
        self.spinBox_line_width.setSuffix(" px")
        width_layout.addWidget(self.spinBox_line_width)
        width_layout.addStretch()
        grid_options_layout.addLayout(width_layout)

        # Anti-Reup Settings
        self.checkbox_random_grid = QCheckBox("🔥 Vị Trí Grid Ngẫu Nhiên (Chống Reup)")
        self.checkbox_dynamic_opacity = QCheckBox("💫 Độ Trong Suốt Động (Hiệu Ứng Thở)")
        self.checkbox_grid_animation = QCheckBox("⚡ Grid Hoạt Hình (Đường Kẻ Di Chuyển)")

        grid_options_layout.addWidget(self.checkbox_random_grid)
        grid_options_layout.addWidget(self.checkbox_dynamic_opacity)
        grid_options_layout.addWidget(self.checkbox_grid_animation)

        # Grid timing
        timing_layout = QHBoxLayout()
        timing_layout.addWidget(QLabel("Thời Gian Grid:"))
        self.comboBox_grid_timing = QComboBox()
        self.comboBox_grid_timing.addItems([
            "Luôn Bật", "Chỉ 3s Đầu", "Chỉ 3s Cuối",
            "Ngẫu Nhiên", "Mờ Dần", "Nhấp Nháy"
        ])
        timing_layout.addWidget(self.comboBox_grid_timing)
        grid_options_layout.addLayout(timing_layout)

        # Anti-Reup intensity
        intensity_layout = QHBoxLayout()
        intensity_layout.addWidget(QLabel("Cường Độ Chống Reup:"))
        self.slider_anti_reup = QSlider(Qt.Horizontal)
        self.slider_anti_reup.setMinimum(1)
        self.slider_anti_reup.setMaximum(10)
        self.slider_anti_reup.setValue(5)
        self.label_intensity = QLabel("5")
        self.slider_anti_reup.valueChanged.connect(lambda v: self.label_intensity.setText(str(v)))
        intensity_layout.addWidget(self.slider_anti_reup)
        intensity_layout.addWidget(self.label_intensity)
        grid_options_layout.addLayout(intensity_layout)

        # Preview button removed for better layout

        self.grid_options_frame.setVisible(True)  # LUÔN HIỂN THỊ GRID OPTIONS
        grid_layout.addWidget(self.grid_options_frame)

        # KHÔNG trigger toggle để grid luôn hiển thị
        # self.toggle_grid_options(self.comboBox_grid_type.currentText())

        print(f"✅ Grid section created and ALWAYS VISIBLE")
        return grid_group

    # AI section removed - was not useful

    # All preview sections removed - keep it simple and focused

    def create_advanced_effects_section(self):
        """Advanced Effects section - MỚI"""
        effects_group = QGroupBox("")  # No title for cleaner look
        effects_group.setStyleSheet("QGroupBox { border: none; }")  # Remove border
        effects_layout = QVBoxLayout(effects_group)

        # Color Grading
        color_group = QGroupBox("🌈 Color Grading")
        color_layout = QVBoxLayout(color_group)

        self.checkbox_cinematic = QCheckBox("🎭 Cinematic Look (Teal & Orange)")
        self.checkbox_vintage = QCheckBox("📼 Vintage Style")
        self.checkbox_hdr = QCheckBox("✨ HDR Tone Mapping")

        color_layout.addWidget(self.checkbox_cinematic)
        color_layout.addWidget(self.checkbox_vintage)
        color_layout.addWidget(self.checkbox_hdr)
        effects_layout.addWidget(color_group)

        # Visual Effects
        visual_group = QGroupBox("✨ Visual Effects")
        visual_layout = QVBoxLayout(visual_group)

        self.checkbox_glitch = QCheckBox("📺 Glitch Effects")
        self.checkbox_light_leaks = QCheckBox("💡 Light Leaks")
        self.checkbox_lens_flare = QCheckBox("🌟 Lens Flare")
        self.checkbox_film_grain = QCheckBox("🎞️ Film Grain")

        visual_layout.addWidget(self.checkbox_glitch)
        visual_layout.addWidget(self.checkbox_light_leaks)
        visual_layout.addWidget(self.checkbox_lens_flare)
        visual_layout.addWidget(self.checkbox_film_grain)
        effects_layout.addWidget(visual_group)

        # Motion Effects
        motion_group = QGroupBox("🏃 Hiệu Ứng Chuyển Động")
        motion_layout = QVBoxLayout(motion_group)

        self.checkbox_motion_blur = QCheckBox("💨 Mờ Chuyển Động")
        self.checkbox_speed_ramping = QCheckBox("⚡ Thay Đổi Tốc Độ")
        self.checkbox_stabilization = QCheckBox("🎯 Chống Rung Video")

        motion_layout.addWidget(self.checkbox_motion_blur)
        motion_layout.addWidget(self.checkbox_speed_ramping)
        motion_layout.addWidget(self.checkbox_stabilization)
        effects_layout.addWidget(motion_group)

        # Particle Effects
        particle_group = QGroupBox("❄️ Hiệu Ứng Hạt")
        particle_layout = QVBoxLayout(particle_group)

        self.checkbox_particles = QCheckBox("Bật Hiệu Ứng Hạt")
        particle_layout.addWidget(self.checkbox_particles)

        particle_type_layout = QHBoxLayout()
        particle_type_layout.addWidget(QLabel("Loại:"))
        self.combo_particle_type = QComboBox()
        self.combo_particle_type.addItems(["Tuyết", "Bụi", "Mưa", "Lấp Lánh"])
        self.combo_particle_type.setEnabled(False)
        particle_type_layout.addWidget(self.combo_particle_type)
        particle_layout.addLayout(particle_type_layout)

        effects_layout.addWidget(particle_group)

        # Connect particle toggle
        self.checkbox_particles.toggled.connect(self.combo_particle_type.setEnabled)

        # Effect intensity
        intensity_layout = QHBoxLayout()
        intensity_layout.addWidget(QLabel("🎚️ Cường Độ Hiệu Ứng:"))
        self.slider_effect_intensity = QSlider(Qt.Horizontal)
        self.slider_effect_intensity.setMinimum(1)
        self.slider_effect_intensity.setMaximum(10)
        self.slider_effect_intensity.setValue(5)
        self.label_effect_intensity = QLabel("5")
        self.slider_effect_intensity.valueChanged.connect(
            lambda v: self.label_effect_intensity.setText(str(v))
        )
        intensity_layout.addWidget(self.slider_effect_intensity)
        intensity_layout.addWidget(self.label_effect_intensity)
        effects_layout.addLayout(intensity_layout)

        return effects_group

    def create_export_section(self):
        """Platform Export section - MỚI"""
        export_group = QGroupBox("📱 Xuất Nền Tảng")
        export_layout = QVBoxLayout(export_group)

        # Platform selection
        platform_layout = QHBoxLayout()
        platform_layout.addWidget(QLabel("🎯 Nền Tảng Đích:"))
        self.combo_platform = QComboBox()
        self.combo_platform.addItems([
            "Tự Động Phát Hiện", "TikTok (9:16)", "Instagram Reels",
            "YouTube Shorts", "Twitter Video", "Facebook Video"
        ])
        platform_layout.addWidget(self.combo_platform)
        export_layout.addLayout(platform_layout)

        # Export options
        self.checkbox_multi_export = QCheckBox("🔄 Xuất Cho Nhiều Nền Tảng")
        export_layout.addWidget(self.checkbox_multi_export)

        # Quality settings
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("Chất Lượng:"))
        self.combo_export_quality = QComboBox()
        self.combo_export_quality.addItems(["Cao", "Trung Bình", "Thấp (Nhanh)"])
        quality_layout.addWidget(self.combo_export_quality)
        export_layout.addLayout(quality_layout)

        return export_group

    def create_processing_section(self):
        """Simple processing info"""
        # Return empty widget since processing section was fake
        return QWidget()

    def create_action_buttons(self):
        """Action buttons"""
        button_widget = QWidget()
        button_layout = QVBoxLayout(button_widget)

        # Main process button
        self.btn_start = QPushButton("🎬 Bắt đầu xử lý")
        self.btn_start.clicked.connect(self.start_processing)
        self.btn_start.setEnabled(False)
        self.btn_start.setMinimumHeight(45)
        self.btn_start.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        button_layout.addWidget(self.btn_start)

        # Preview effects button removed for cleaner layout

        return button_widget



    # Event handlers và methods từ UI cũ
    def add_video_file(self, file_path):
        """Add video file to list"""
        self.video_paths.append(file_path)
        self.update_file_count()

    def select_files(self):
        """Select files dialog"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "Chọn video files", "",
            "Video Files (*.mp4 *.mov *.avi *.mkv *.flv *.wmv);;All Files (*)"
        )

        for file_path in files:
            self.file_list.add_video_file(file_path)
            self.add_video_file(file_path)

    def clear_files(self):
        """Clear all files"""
        self.file_list.clear_files()
        self.video_paths.clear()
        self.update_file_count()

    def update_file_count(self):
        """Update file count display with auto-processing info"""
        count = len(self.video_paths)
        if count > 0:
            # 🚀 FIXED LOGIC: GPU First, Multi-threading only for 4+ videos
            system_info = self.detect_system_capabilities()
            if count <= 3:
                # 1-3 videos: Sequential GPU processing
                if count == 1:
                    mode_info = "🚀 Sẽ dùng GPU" if system_info['has_gpu'] else "💻 Sẽ dùng CPU"
                else:
                    mode_info = f"🚀 GPU Sequential ({count} videos)" if system_info['has_gpu'] else f"💻 CPU Sequential ({count} videos)"
            else:
                # 4+ videos: Multi-threading
                if system_info['has_gpu']:
                    threads = min(system_info['optimal_threads'], count, 4)
                    mode_info = f"🔥 GPU + CPU ({threads} Threads Multi-Processing)"
                else:
                    threads = min(system_info['optimal_threads'], count)
                    mode_info = f"💻 CPU Only ({threads} Threads - No GPU)"

            self.label_selected.setText(f"✅ Đã chọn {count} video files | {mode_info}")
            self.btn_start.setEnabled(True)
            # Enable preview if checkbox is checked
            if hasattr(self, 'checkbox_preview') and self.checkbox_preview.isChecked():
                self.btn_preview_start.setEnabled(True)
        else:
            self.label_selected.setText("🖱️ Kéo thả video vào khung trên hoặc click vào khung để chọn files")
            self.btn_start.setEnabled(False)
            if hasattr(self, 'btn_preview_start'):
                self.btn_preview_start.setEnabled(False)



    def toggle_grid_options(self, grid_type):
        """Toggle grid options visibility - ALWAYS SHOW"""
        # LUÔN HIỂN THỊ GRID OPTIONS
        show_options = True  # Luôn True để grid luôn hiển thị
        if hasattr(self, 'grid_options_frame'):
            self.grid_options_frame.setVisible(show_options)
            print(f"🔧 Grid options ALWAYS VISIBLE for type: {grid_type}")
        else:
            print("⚠️ grid_options_frame not yet created")

    def update_opacity_label(self, value):
        """Update opacity label"""
        self.label_opacity_value.setText(f"{value}%")

    # preview_grid_settings method removed for cleaner code

    def toggle_preview_controls(self, enabled):
        """Toggle preview controls"""
        self.btn_preview_start.setEnabled(enabled and len(self.video_paths) > 0)
        self.btn_preview_stop.setEnabled(False)

    def start_preview(self):
        """Start real-time preview"""
        if not self.video_paths:
            QMessageBox.warning(self, "Warning", "Please select a video first!")
            return

        self.log_message("🎮 Starting real-time preview...")
        self.btn_preview_start.setEnabled(False)
        self.btn_preview_stop.setEnabled(True)

        # Start preview in separate thread
        def preview_worker():
            try:
                video_path = self.video_paths[0]
                effects = self.collect_effects_settings()
                quality = self.combo_preview_quality.currentText()

                self.log_message(f"👁️ Previewing: {video_path.split('/')[-1]}")
                self.log_message(f"🎛️ Quality: {quality}")
                self.log_message(f"⚙️ Effects: {len([k for k, v in effects.items() if v])} enabled")

                # Simulate preview processing
                import time
                for i in range(10):
                    if hasattr(self, 'preview_stopped') and self.preview_stopped:
                        break
                    time.sleep(0.5)
                    self.log_message(f"🎬 Preview frame {i+1}/10 processed")

                if not (hasattr(self, 'preview_stopped') and self.preview_stopped):
                    self.log_message("✅ Preview completed! Check preview window.")

            except Exception as e:
                self.log_message(f"❌ Preview error: {str(e)}")
            finally:
                # Reset buttons
                self.btn_preview_start.setEnabled(True)
                self.btn_preview_stop.setEnabled(False)

        self.preview_stopped = False
        import threading
        threading.Thread(target=preview_worker, daemon=True).start()

    def stop_preview(self):
        """Stop real-time preview"""
        self.preview_stopped = True
        self.log_message("⏹️ Preview stopped")
        self.btn_preview_start.setEnabled(True)
        self.btn_preview_stop.setEnabled(False)

    def analyze_video_quality(self):
        """Analyze video quality with AI and AUTO-APPLY recommendations"""
        if not self.video_paths:
            QMessageBox.warning(self, "Warning", "Vui lòng chọn video trước!")
            return

        if not self.ai_enhancer:
            QMessageBox.warning(self, "Warning", "AI Enhancer không khả dụng!")
            return

        video_path = self.video_paths[0]
        self.log_message("🤖 Đang phân tích video với AI...")

        def analyze():
            try:
                # Use QTimer to update UI safely from thread
                def update_ui_safe(text):
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(0, lambda: self.ai_recommendations.setText(text))

                def log_safe(message):
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(0, lambda: self.log_message(message))

                log_safe("🤖 Bắt đầu phân tích AI...")

                success, analysis = self.ai_enhancer.analyze_video_content(video_path)
                if success:
                    # Display recommendations text
                    recommendations_text = "🤖 AI Gợi Ý & Tự Động Áp Dụng:\n"
                    recommendations_text += f"📊 Điểm chất lượng: {analysis.get('quality_score', 0)}/100\n\n"

                    applied_effects = []

                    for rec in analysis.get('recommendations', []):
                        recommendations_text += f"• {rec['reason']}\n"

                        # AUTO-APPLY RECOMMENDATIONS (safely)
                        try:
                            effect_applied = self.apply_ai_recommendation(rec)
                            if effect_applied:
                                applied_effects.append(effect_applied)
                        except Exception as e:
                            log_safe(f"⚠️ Lỗi áp dụng hiệu ứng: {str(e)}")

                    if applied_effects:
                        recommendations_text += f"\n✅ Đã tự động bật: {', '.join(applied_effects)}"
                        recommendations_text += f"\n👀 Bạn có thể điều chỉnh các hiệu ứng bên trái nếu cần!"

                    update_ui_safe(recommendations_text)
                    log_safe(f"✅ AI phân tích hoàn tất. Điểm: {analysis.get('quality_score', 0)}/100")
                    log_safe(f"🎯 Đã tự động áp dụng: {', '.join(applied_effects) if applied_effects else 'Không có gợi ý'}")
                else:
                    log_safe(f"❌ AI phân tích thất bại: {analysis}")
            except Exception as e:
                log_safe(f"💥 Lỗi AI phân tích: {str(e)}")
                import traceback
                log_safe(f"🔍 Chi tiết lỗi: {traceback.format_exc()}")

        import threading
        threading.Thread(target=analyze, daemon=True).start()

    def apply_ai_recommendation(self, recommendation):
        """Apply AI recommendation by auto-checking effects"""
        rec_type = recommendation.get('type', '')

        if rec_type == 'brightness':
            # Enable color shift for brightness adjustment
            self.checkbox_color_shift.setChecked(True)
            return "Color Shift (Điều chỉnh sáng)"

        elif rec_type == 'contrast':
            # Enable color shift for contrast adjustment
            self.checkbox_color_shift.setChecked(True)
            return "Color Shift (Tăng tương phản)"

        elif rec_type == 'sharpness':
            # Enable Wink enhancement for sharpness
            self.checkbox_wink_enhance.setChecked(True)
            return "Wink Enhancement (Tăng độ sắc nét 5500%+ như Wink)"

        elif rec_type == 'face_enhancement':
            # Enable Wink for face enhancement
            self.checkbox_wink_enhance.setChecked(True)
            return "Wink Enhancement (Làm đẹp khuôn mặt 5500%+ sharpening)"

        elif rec_type == 'stabilization':
            # Enable motion blur reduction
            if hasattr(self, 'checkbox_stabilization'):
                self.checkbox_stabilization.setChecked(True)
                return "Video Stabilization (Chống rung)"
            else:
                # Fallback to blur effect
                self.checkbox_blur.setChecked(True)
                return "Blur Effect (Giảm rung)"

        elif rec_type == 'scene_enhancement':
            # Enable multiple effects for scene enhancement
            self.checkbox_color_shift.setChecked(True)
            self.checkbox_wink_enhance.setChecked(True)
            return "Multi-Effect + Wink (Cải thiện cảnh với 5500%+ sharpening)"

        return None

    def reset_ai_suggestions(self):
        """Reset all AI suggestions and clear effects"""
        # Clear AI recommendations text
        self.ai_recommendations.clear()
        self.ai_recommendations.setPlaceholderText("🤖 Chọn video và click 'Phân Tích & Tự Động Gợi Ý' để AI tự động tick hiệu ứng phù hợp!")

        # Uncheck all effects that might be set by AI
        self.checkbox_color_shift.setChecked(False)
        self.checkbox_wink_enhance.setChecked(False)
        self.checkbox_blur.setChecked(False)

        # Uncheck advanced effects if they exist
        if hasattr(self, 'checkbox_cinematic'):
            self.checkbox_cinematic.setChecked(False)
        if hasattr(self, 'checkbox_stabilization'):
            self.checkbox_stabilization.setChecked(False)

        # Reset Wink enhancement
        # (No level selector to hide anymore)

        self.log_message("🔄 Đã reset tất cả gợi ý AI")

    def detect_system_capabilities(self):
        """🔍 Detect system GPU and CPU capabilities"""
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            # GPU detection
            has_gpu, gpu_types = processor.check_gpu_support()

            # CPU threads detection
            optimal_threads = processor.get_optimal_threads()

            return {
                'has_gpu': has_gpu,
                'gpu_types': gpu_types,
                'optimal_threads': optimal_threads
            }
        except Exception as e:
            # Fallback detection
            import os
            cpu_count = os.cpu_count() or 4
            return {
                'has_gpu': False,
                'gpu_types': [],
                'optimal_threads': min(cpu_count, 4)
            }

    def start_processing(self):
        """Start video processing"""
        if not self.video_paths:
            QMessageBox.warning(self, "Warning", "Please select video files first!")
            return

        # Collect effects settings
        effects = self.collect_effects_settings()

        # Get zoom percentage from slider
        zoom_percent = self.slider_zoom.value()

        # 🚀 INTELLIGENT GPU/CPU AUTO-DETECTION
        video_count = len(self.video_paths)
        system_info = self.detect_system_capabilities()

        # 🚀 FIXED LOGIC: GPU First, Multi-threading only for 4+ videos
        if video_count <= 3:
            # 1-3 videos: Always use GPU sequential processing (no multi-threading)
            use_gpu = system_info['has_gpu']
            num_threads = 1  # Sequential GPU processing
            if video_count == 1:
                processing_mode = "🚀 GPU (Single Video)" if use_gpu else "💻 CPU (No GPU)"
            else:
                processing_mode = f"🚀 GPU Sequential ({video_count} videos)" if use_gpu else f"💻 CPU Sequential ({video_count} videos)"
        else:
            # 4+ videos: Use multi-threading
            if system_info['has_gpu']:
                # GPU available: Use GPU + CPU multi-threading
                use_gpu = True
                num_threads = min(system_info['optimal_threads'], video_count, 4)  # Multi-threading
                processing_mode = f"🔥 GPU + CPU ({num_threads} Threads Multi-Processing)"
            else:
                # No GPU: CPU only multi-threading
                use_gpu = False
                num_threads = min(system_info['optimal_threads'], video_count)
                processing_mode = f"💻 CPU Only ({num_threads} Threads - No GPU)"

        # Apply GPU setting to effects (auto-controlled)
        effects['use_gpu'] = use_gpu

        # Start processing
        self.log_message(f"🚀 Bắt đầu xử lý {video_count} video(s)...")
        self.log_message(f"🤖 Chế độ tự động: {processing_mode}")
        self.log_message(f"⚙️ Threads: {num_threads} | GPU: {'Có' if use_gpu else 'Không'}")

        self.btn_start.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.start_time = time.time()

        # Create and start processor thread
        self.processor_thread = VideoProcessorThread(
            self.video_paths, effects, zoom_percent, num_threads
        )

        self.processor_thread.progress_updated.connect(self.update_progress)
        self.processor_thread.status_updated.connect(self.log_message)
        self.processor_thread.finished.connect(self.processing_finished)
        self.processor_thread.error_occurred.connect(self.processing_error)

        self.processor_thread.start()

    def collect_effects_settings(self):
        """Collect all effects settings"""
        effects = {}

        # 🔥 ADVANCED BYPASS TECHNIQUES
        if self.checkbox_audio.isChecked():
            effects['audio'] = True
        if self.checkbox_color_shift.isChecked():
            effects['color_shift'] = True
        if hasattr(self, 'checkbox_geometric') and self.checkbox_geometric.isChecked():
            effects['geometric_bypass'] = True
        if hasattr(self, 'checkbox_temporal') and self.checkbox_temporal.isChecked():
            effects['temporal_bypass'] = True
        if hasattr(self, 'checkbox_metadata') and self.checkbox_metadata.isChecked():
            effects['metadata_bypass'] = True

        # Basic effects
        if self.checkbox_shift.isChecked():
            effects['shift'] = True
        if self.checkbox_blur.isChecked():
            effects['blur'] = True
        if self.checkbox_noise.isChecked():
            effects['noise'] = True
        if self.checkbox_vibration.isChecked():
            effects['vibration'] = True
        if self.checkbox_overlay.isChecked():
            effects['overlay'] = True
        if hasattr(self, 'checkbox_frame_black') and self.checkbox_frame_black.isChecked():
            effects['blackframe'] = True
        # GPU is now auto-controlled in start_processing()
        if hasattr(self, 'checkbox_speed') and self.checkbox_speed.isChecked():
            effects['speed'] = True
        if self.checkbox_wink_enhance.isChecked():
            effects['wink_enhancement'] = True
            effects['wink_level'] = "high"  # Always use best quality

        # Grid effects - FIX MAPPING VIỆT HÓA
        grid_type = self.comboBox_grid_type.currentText()
        if grid_type != "Không (Tắt)":
            effects['guides'] = True

            # Map UI names to processor names - VIỆT HÓA
            grid_type_map = {
                "3x3 (Quy Tắc 1/3)": "3x3",
                "4x4 Grid": "4x4",
                "5x5 Grid": "5x5",
                "6x6 Grid": "6x6",
                "Thập Tự Giữa": "center_cross",
                "Tỷ Lệ Vàng": "golden_ratio",
                "Đường Chéo": "diagonal",
                "🔥 Grid Ngẫu Nhiên (Chống Reup)": "random_grid",
                "🎯 Grid Động (Di Chuyển)": "dynamic_grid",
                "⚡ Grid Bất Đối Xứng": "asymmetric",
                "🌟 Xoắn Fibonacci": "fibonacci",
                "💫 Grid Lục Giác": "hexagon",
                "🎨 Mẫu Tùy Chỉnh": "custom_pattern",
                "🔀 Grid Đa Lớp": "multi_layer",
                # NEW COMPREHENSIVE PATTERNS
                "🔴 Dots Pattern (Chấm Tròn)": "dots_pattern",
                "🔺 Triangle Pattern (Tam Giác)": "triangle_pattern",
                "⭐ Star Pattern (Ngôi Sao)": "star_pattern",
                "🌊 Wave Pattern (Sóng)": "wave_pattern",
                "💎 Diamond Pattern (Kim Cương)": "diamond_pattern",
                "🔶 Rhombus Pattern (Thoi)": "rhombus_pattern",
                "🌀 Spiral Pattern (Xoắn Ốc)": "spiral_pattern",
                "⚡ Zigzag Pattern (Răng Cưa)": "zigzag_pattern",
                "🎯 Target Pattern (Bia)": "target_pattern",
                "🕸️ Web Pattern (Mạng Nhện)": "web_pattern",
                "🔳 Border Frame (Khung Viền)": "border_frame",
                "📐 Geometric Mix (Hình Học)": "geometric_mix",
                "🎪 Circus Pattern (Xiếc)": "circus_pattern",
                "🌈 Rainbow Arch (Cầu Vồng)": "rainbow_arch",
                "❄️ Snowflake Pattern (Bông Tuyết)": "snowflake_pattern",
                "🌸 Flower Pattern (Hoa)": "flower_pattern",
                "⚙️ Gear Pattern (Bánh Răng)": "gear_pattern",
                "🔥 Fire Pattern (Lửa)": "fire_pattern"
            }

            mapped_type = grid_type_map.get(grid_type, "3x3")

            # Map color names
            color_map = {
                "Trắng (White)": "white",
                "Đỏ (Red)": "red",
                "Xanh Dương (Blue)": "blue",
                "Xanh Lá (Green)": "green",
                "Vàng (Yellow)": "yellow",
                "Tím (Purple)": "purple",
                "Cam (Orange)": "orange",
                "Hồng (Pink)": "pink",
                "🌈 Rainbow (Multi-Color)": "rainbow",
                "🔥 Random Color": "random_color",
                "💫 Gradient": "gradient"
            }

            color_text = self.comboBox_grid_color.currentText()
            mapped_color = color_map.get(color_text, "white")

            effects['grid_config'] = {
                'type': mapped_type,
                'color': mapped_color,
                'opacity': self.slider_opacity.value() / 100.0,
                'line_width': self.spinBox_line_width.value(),  # Fix key name
                'random_position': self.checkbox_random_grid.isChecked(),
                'dynamic_opacity': self.checkbox_dynamic_opacity.isChecked(),
                'animated': self.checkbox_grid_animation.isChecked(),  # Fix key name
                'timing': self.comboBox_grid_timing.currentText().lower().replace(' ', '_'),
                'anti_reup_intensity': self.slider_anti_reup.value()  # Fix key name
            }

        # AI effects (removed - no longer needed)
        # if self.ai_enhancer and self.checkbox_ai_auto.isChecked():
        #     effects['ai_auto'] = True

        # Preview settings
        if hasattr(self, 'checkbox_preview') and self.checkbox_preview.isChecked():
            effects['preview_enabled'] = True
            effects['preview_quality'] = self.combo_preview_quality.currentText()

        # Advanced Effects
        if hasattr(self, 'checkbox_cinematic'):
            advanced_effects = {}
            if self.checkbox_cinematic.isChecked():
                advanced_effects['cinematic_look'] = True
            if self.checkbox_vintage.isChecked():
                advanced_effects['vintage_style'] = True
            if self.checkbox_hdr.isChecked():
                advanced_effects['hdr_tone_mapping'] = True
            if self.checkbox_glitch.isChecked():
                advanced_effects['glitch_effect'] = True
            if self.checkbox_light_leaks.isChecked():
                advanced_effects['light_leaks'] = True
            if self.checkbox_lens_flare.isChecked():
                advanced_effects['lens_flare'] = True
            if self.checkbox_film_grain.isChecked():
                advanced_effects['film_grain'] = True
            if self.checkbox_motion_blur.isChecked():
                advanced_effects['motion_blur'] = True
                advanced_effects['motion_blur_intensity'] = self.slider_effect_intensity.value()
            if self.checkbox_speed_ramping.isChecked():
                advanced_effects['speed_ramping'] = True
            if self.checkbox_stabilization.isChecked():
                advanced_effects['video_stabilization'] = True
            if self.checkbox_particles.isChecked():
                advanced_effects['particle_effects'] = True
                # Map Vietnamese particle types to English
                particle_map = {
                    "Tuyết": "snow",
                    "Bụi": "dust",
                    "Mưa": "rain",
                    "Lấp Lánh": "sparkles"
                }
                particle_text = self.combo_particle_type.currentText()
                advanced_effects['particle_type'] = particle_map.get(particle_text, "snow")

            if advanced_effects:
                effects['advanced_effects'] = advanced_effects

        # Export settings
        if hasattr(self, 'combo_platform'):
            # Map Vietnamese platform names to English
            platform_map = {
                "Tự Động Phát Hiện": "Auto Detect",
                "TikTok (9:16)": "TikTok (9:16)",
                "Instagram Reels": "Instagram Reels",
                "YouTube Shorts": "YouTube Shorts",
                "Twitter Video": "Twitter Video",
                "Facebook Video": "Facebook Video"
            }
            platform_text = self.combo_platform.currentText()
            effects['target_platform'] = platform_map.get(platform_text, "Auto Detect")

            # Map Vietnamese quality names to English
            quality_map = {
                "Cao": "High",
                "Trung Bình": "Medium",
                "Thấp (Nhanh)": "Low (Fast)"
            }
            quality_text = self.combo_export_quality.currentText()
            effects['export_quality'] = quality_map.get(quality_text, "High")

            effects['multi_export'] = self.checkbox_multi_export.isChecked()

        # 🚀 Performance optimization settings (with safe defaults)
        # Removed fake performance settings

        return effects

    def update_progress(self, percent):
        """Update progress bar"""
        self.progress_bar.setValue(percent)

        if self.start_time:
            elapsed = time.time() - self.start_time
            if percent > 0:
                estimated_total = elapsed * 100 / percent
                remaining = estimated_total - elapsed
                self.label_progress.setText(f"Progress: {percent}% - Estimated remaining: {remaining:.1f}s")
            else:
                self.label_progress.setText(f"Progress: {percent}% - Elapsed: {elapsed:.1f}s")

    def log_message(self, message):
        """Add message to log with filtering"""
        # Filter out spam messages
        spam_filters = [
            "Unknown property",
            "DeprecationWarning",
            "Found FFmpeg at:",
            "sipPyTypeDict",
            "transition",
            "box-shadow"
        ]

        # Skip spam messages
        if any(spam in str(message) for spam in spam_filters):
            return

        timestamp = QTime.currentTime().toString("hh:mm:ss")
        self.text_log.append(f"[{timestamp}] {message}")

        # Auto scroll to bottom
        cursor = self.text_log.textCursor()
        cursor.movePosition(cursor.End)
        self.text_log.setTextCursor(cursor)

    def processing_finished(self):
        """Processing finished"""
        self.btn_start.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.label_progress.setText("✅ Processing completed!")
        self.log_message("🎉 All videos processed successfully!")

    def processing_error(self, error_msg):
        """Processing error occurred"""
        self.btn_start.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.label_progress.setText("❌ Processing failed!")
        self.log_message(f"💥 Error: {error_msg}")
        QMessageBox.critical(self, "Processing Error", error_msg)

    def clear_logs(self):
        """Clear log display"""
        self.text_log.clear()
        self.log_message("📝 Logs cleared")

    def save_logs(self):
        """Save logs to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Logs", "processing_logs.txt", "Text Files (*.txt);;All Files (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.text_log.toPlainText())
                self.log_message(f"💾 Logs saved to: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Save Error", f"Failed to save logs: {str(e)}")

    def expand_logs(self):
        """Expand logs in separate window"""
        # Create expanded logs window
        logs_window = QDialog(self)
        logs_window.setWindowTitle("📋 Expanded Logs")
        logs_window.setGeometry(100, 100, 800, 600)

        layout = QVBoxLayout(logs_window)

        # Expanded text edit
        expanded_log = QTextEdit()
        expanded_log.setReadOnly(True)
        expanded_log.setPlainText(self.text_log.toPlainText())
        expanded_log.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                color: #198754;
            }
        """)
        layout.addWidget(expanded_log)

        # Close button
        close_btn = QPushButton("✅ Close")
        close_btn.clicked.connect(logs_window.close)
        layout.addWidget(close_btn)

        logs_window.exec_()

# Test launcher
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    window = EnhancedCompleteUI()
    window.show()  # Explicitly show the window
    sys.exit(app.exec_())
