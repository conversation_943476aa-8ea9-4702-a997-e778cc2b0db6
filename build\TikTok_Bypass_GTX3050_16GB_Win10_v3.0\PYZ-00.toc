('C:\\Users\\<USER>\\Desktop\\FINAL\\build\\TikTok_Bypass_GTX3050_16GB_Win10_v3.0\\PYZ-00.pyz',
 [('PyQt5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('PyQt5.Qt5', '-', 'PYMODULE'),
  ('PyQt5.pylupdate_main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\pylupdate_main.py',
   'PYMODULE'),
  ('PyQt5.pyrcc_main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\pyrcc_main.py',
   'PYMODULE'),
  ('PyQt5.uic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.indenter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.misc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.proxy_metaclass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qobjectcreator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qtproxies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.qobjectcreator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.driver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\driver.py',
   'PYMODULE'),
  ('PyQt5.uic.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt5.uic.icon_cache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt5.uic.objcreator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\port_v3\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.as_string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\port_v3\\as_string.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.ascii_upper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\port_v3\\ascii_upper.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.proxy_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\port_v3\\proxy_base.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.string_io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\port_v3\\string_io.py',
   'PYMODULE'),
  ('PyQt5.uic.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt5.uic.pyuic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\pyuic.py',
   'PYMODULE'),
  ('PyQt5.uic.uiparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyQt5\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('advanced_grid_processor',
   'C:\\Users\\<USER>\\Desktop\\FINAL\\advanced_grid_processor.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\calendar.py',
   'PYMODULE'),
  ('cmd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\codeop.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('enhanced_ui_complete',
   'C:\\Users\\<USER>\\Desktop\\FINAL\\enhanced_ui_complete.py',
   'PYMODULE'),
  ('fnmatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\optparse.py',
   'PYMODULE'),
  ('pathlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\random.py',
   'PYMODULE'),
  ('readline',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('runpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\signal.py',
   'PYMODULE'),
  ('smtplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\typing.py',
   'PYMODULE'),
  ('ui_config', 'C:\\Users\\<USER>\\Desktop\\FINAL\\ui_config.py', 'PYMODULE'),
  ('unittest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uu.py',
   'PYMODULE'),
  ('video_processor',
   'C:\\Users\\<USER>\\Desktop\\FINAL\\video_processor.py',
   'PYMODULE'),
  ('webbrowser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipimport.py',
   'PYMODULE')])
