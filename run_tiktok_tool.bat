@echo off
echo 🎬 Starting Edit Short / Tu Anh Tran...
echo =======================================

REM Change to the script directory
cd /d "%~dp0"

REM Try to use conda python first
set CONDA_PATH=C:\Users\<USER>\.conda\envs\tiktok_env\python.exe

if exist "%CONDA_PATH%" (
    echo ✅ Using conda environment python
    echo 🎬 Launching Enhanced TikTok Bypass Tool...
    "%CONDA_PATH%" launch_complete.py
) else (
    echo ⚠️ Conda environment not found, trying system python...
    echo 🎬 Launching Enhanced TikTok Bypass Tool...
    python launch_complete.py
)

REM Keep window open if there's an error
if errorlevel 1 (
    echo ❌ Error occurred while running the tool
    pause
) else (
    echo 🎯 Tool finished successfully!
)

pause
