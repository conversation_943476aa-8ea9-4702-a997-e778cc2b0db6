@echo off
echo 🎬 Building Edit Short / Tu Anh Tran EXE
echo ==========================================

echo 📦 Installing PyInstaller...
pip install pyinstaller

echo 🔨 Building EXE (NO CONSOLE - CLEAN VERSION)...
pyinstaller --onefile --windowed --noconsole ^
    --name "Edit_Short_Tu_Anh_Tran" ^
    --add-data "ffmpeg;ffmpeg" ^
    --exclude-module matplotlib ^
    --exclude-module scipy ^
    --exclude-module pandas ^
    --exclude-module jupyter ^
    --exclude-module IPython ^
    --exclude-module tkinter ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --collect-all=PyQt5 ^
    enhanced_ui_complete.py

echo ✅ Clean build completed!
echo 📁 EXE file location: dist/Edit_Short_Tu_Anh_Tran.exe
echo 🎯 NO CONSOLE/CMD windows will appear when running!
echo 🚀 Ready to copy to other machines!
pause
