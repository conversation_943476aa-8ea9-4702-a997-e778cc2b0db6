#!/usr/bin/env python3
"""
Launch Complete UI - Khởi chạy UI hoàn chỉnh
UI với tất cả tính năng cũ + mới, theme sáng đẹp
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def check_dependencies():
    """Kiểm tra dependencies"""
    missing = []
    
    # Core dependencies
    try:
        import PyQt5
    except ImportError:
        missing.append("PyQt5")
    
    try:
        import cv2
    except ImportError:
        missing.append("opencv-python")
    
    # Check core modules
    if not os.path.exists("video_processor.py"):
        missing.append("video_processor.py")
    
    if not os.path.exists("ui_config.py"):
        missing.append("ui_config.py")
    
    if not os.path.exists("enhanced_ui_complete.py"):
        missing.append("enhanced_ui_complete.py")
    
    return missing

def setup_application():
    """Thiết lập application"""
    # Enable high DPI support
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setApplicationName("Enhanced TikTok Bypass Tool")
    app.setApplicationVersion("3.0 Complete")
    app.setOrganizationName("Enhanced Video Tools")
    
    return app

def main():
    """Main launcher"""
    print("🚀 Enhanced TikTok Bypass Tool v3.0 Complete")
    print("=" * 60)
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print("❌ Missing dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n📦 Please install missing dependencies and ensure all files are present.")
        input("Press Enter to exit...")
        return 1
    
    # Setup application
    app = setup_application()
    
    try:
        # Import and create UI
        from enhanced_ui_complete import EnhancedCompleteUI
        
        print("🎬 Initializing Enhanced Complete UI...")
        window = EnhancedCompleteUI()
        
        print("✅ Enhanced TikTok Bypass Tool started successfully!")
        print("🎯 All features available:")
        print("   • 📁 Drag & drop file selection")
        print("   • 🎭 All bypass effects (shift, blur, audio, etc.)")
        print("   • 📏 Complete grid system (15+ types)")
        print("   • 🔥 Anti-reupload features")
        print("   • 🤖 AI smart enhancement")
        print("   • ✨ Video enhancement")
        print("   • ⚙️ Multi-threading support")
        print("   • 📊 Real-time progress tracking")
        print("   • 📋 Comprehensive logging")
        print("   • 🎨 Beautiful light theme")
        
        return app.exec_()
        
    except ImportError as e:
        error_msg = f"Failed to import UI modules:\n{str(e)}\n\nMake sure all files are present."
        print(f"❌ {error_msg}")
        try:
            QMessageBox.critical(None, "Import Error", error_msg)
        except:
            pass
        return 1
        
    except Exception as e:
        error_msg = f"Failed to start application:\n{str(e)}"
        print(f"💥 {error_msg}")
        try:
            QMessageBox.critical(None, "Startup Error", error_msg)
        except:
            pass
        return 1

if __name__ == "__main__":
    sys.exit(main())
