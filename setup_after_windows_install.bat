@echo off
chcp 65001 >nul
echo.
echo ============================================================================
echo 🚀 ENHANCED TIKTOK BYPASS TOOL - WINDOWS SETUP SCRIPT
echo ============================================================================
echo Version: 3.0 Complete
echo Compatible with: Windows 10/11
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Please run as Administrator for best results
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo 📋 This script will install:
echo    • Python 3.11 (if not installed)
echo    • Git (if not installed)
echo    • All required Python packages
echo    • Setup project environment
echo.
pause

:: Create logs directory
if not exist "logs" mkdir logs
set LOGFILE=logs\setup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
echo Setup started at %date% %time% > "%LOGFILE%"

echo.
echo ============================================================================
echo 📦 STEP 1: CHECKING SYSTEM REQUIREMENTS
echo ============================================================================

:: Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows Version: %VERSION%
echo Windows Version: %VERSION% >> "%LOGFILE%"

:: Check if Python is installed
echo.
echo 🐍 Checking Python installation...
python --version >nul 2>&1
if %errorLevel% == 0 (
    python --version
    echo ✅ Python is already installed
    python --version >> "%LOGFILE%"
) else (
    echo ❌ Python not found. Installing Python 3.11...
    echo Downloading Python installer...
    
    :: Download Python 3.11
    powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-amd64.exe' -OutFile 'python-installer.exe'"
    
    if exist python-installer.exe (
        echo Installing Python 3.11...
        python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
        timeout /t 30 /nobreak
        del python-installer.exe
        
        :: Refresh environment variables
        call refreshenv
        
        :: Check again
        python --version >nul 2>&1
        if %errorLevel% == 0 (
            echo ✅ Python installed successfully
            python --version >> "%LOGFILE%"
        ) else (
            echo ❌ Python installation failed. Please install manually.
            echo Python installation failed >> "%LOGFILE%"
            pause
            exit /b 1
        )
    ) else (
        echo ❌ Failed to download Python installer
        echo Please download and install Python 3.11 manually from python.org
        pause
        exit /b 1
    )
)

:: Check if Git is installed
echo.
echo 📂 Checking Git installation...
git --version >nul 2>&1
if %errorLevel% == 0 (
    git --version
    echo ✅ Git is already installed
    git --version >> "%LOGFILE%"
) else (
    echo ❌ Git not found. Please install Git manually from git-scm.com
    echo Git is recommended for version control
    echo Git not found >> "%LOGFILE%"
)

echo.
echo ============================================================================
echo 📦 STEP 2: SETTING UP PYTHON ENVIRONMENT
echo ============================================================================

:: Upgrade pip
echo 🔄 Upgrading pip...
python -m pip install --upgrade pip
if %errorLevel% == 0 (
    echo ✅ Pip upgraded successfully
    echo Pip upgraded successfully >> "%LOGFILE%"
) else (
    echo ⚠️ Pip upgrade failed, continuing...
    echo Pip upgrade failed >> "%LOGFILE%"
)

:: Install core requirements
echo.
echo 📦 Installing core requirements...
echo This may take several minutes...

python -m pip install -r requirements.txt
if %errorLevel% == 0 (
    echo ✅ Core requirements installed successfully
    echo Core requirements installed >> "%LOGFILE%"
) else (
    echo ❌ Failed to install some requirements
    echo Trying to install core packages individually...
    echo Failed to install requirements, trying individually >> "%LOGFILE%"
    
    :: Install core packages one by one
    echo Installing PyQt5...
    python -m pip install PyQt5==5.15.11
    
    echo Installing OpenCV...
    python -m pip install opencv-python==*********
    
    echo Installing NumPy...
    python -m pip install numpy==2.2.6
    
    echo Installing Pillow...
    python -m pip install Pillow==11.2.1
    
    echo Installing other utilities...
    python -m pip install psutil tqdm requests
)

echo.
echo ============================================================================
echo 🧪 STEP 3: TESTING INSTALLATION
echo ============================================================================

echo 🧪 Testing Python imports...
python -c "import PyQt5; print('✅ PyQt5 OK')" 2>>"%LOGFILE%"
python -c "import cv2; print('✅ OpenCV OK')" 2>>"%LOGFILE%"
python -c "import numpy; print('✅ NumPy OK')" 2>>"%LOGFILE%"
python -c "import PIL; print('✅ Pillow OK')" 2>>"%LOGFILE%"

echo.
echo 🧪 Testing tool launch...
timeout /t 3 /nobreak >nul
python -c "
try:
    from enhanced_ui_complete import EnhancedCompleteUI
    print('✅ Enhanced UI import OK')
except Exception as e:
    print(f'❌ Enhanced UI import failed: {e}')
" 2>>"%LOGFILE%"

echo.
echo ============================================================================
echo ✅ SETUP COMPLETED!
echo ============================================================================
echo.
echo 🎯 Next steps:
echo    1. Run: python launch_complete.py
echo    2. Test drag & drop functionality
echo    3. Process a small video file
echo.
echo 📋 Log file saved to: %LOGFILE%
echo.
echo 🚀 Enhanced TikTok Bypass Tool is ready to use!
echo.
pause
