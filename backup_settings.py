#!/usr/bin/env python3
"""
Backup Settings - <PERSON><PERSON><PERSON> cài đặt quan trọng trướ<PERSON> khi cài Windows
"""

import os
import json
import shutil
import zipfile
from datetime import datetime
import sys

def create_backup():
    """Tạo backup c<PERSON><PERSON> tất cả settings và files quan trọng"""
    
    print("🔄 Creating backup of Enhanced TikTok Bypass Tool...")
    
    # Tạo thư mục backup với timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # Files cần backup
    important_files = [
        "launch_complete.py",
        "enhanced_ui_complete.py", 
        "requirements.txt",
        "video_processor.py",
        "ui_config.py",
        "advanced_grid_processor.py",
        "ai_smart_enhancer_safe.py",
        "debug_toolkit.py",
        "utils.py",
        "README.md",
        "QUICK_START.md",
        "BUILD_INSTRUCTIONS.md"
    ]
    
    # Backup important files
    print("📁 Backing up important files...")
    files_backed_up = []
    
    for file in important_files:
        if os.path.exists(file):
            try:
                shutil.copy2(file, backup_dir)
                files_backed_up.append(file)
                print(f"   ✅ {file}")
            except Exception as e:
                print(f"   ❌ {file}: {e}")
        else:
            print(f"   ⚠️ {file}: Not found")
    
    # Backup directories
    important_dirs = [
        "ffmpeg",
        "_processed",
        "backup_manual"
    ]
    
    print("\n📂 Backing up directories...")
    dirs_backed_up = []
    
    for dir_name in important_dirs:
        if os.path.exists(dir_name):
            try:
                backup_path = os.path.join(backup_dir, dir_name)
                shutil.copytree(dir_name, backup_path)
                dirs_backed_up.append(dir_name)
                print(f"   ✅ {dir_name}/")
            except Exception as e:
                print(f"   ❌ {dir_name}/: {e}")
        else:
            print(f"   ⚠️ {dir_name}/: Not found")
    
    # Tạo system info
    print("\n💻 Collecting system info...")
    system_info = {
        "backup_date": datetime.now().isoformat(),
        "python_version": sys.version,
        "platform": sys.platform,
        "files_backed_up": files_backed_up,
        "dirs_backed_up": dirs_backed_up,
        "total_files": len(files_backed_up),
        "total_dirs": len(dirs_backed_up)
    }
    
    # Lưu system info
    with open(os.path.join(backup_dir, "backup_info.json"), "w", encoding="utf-8") as f:
        json.dump(system_info, f, indent=2, ensure_ascii=False)
    
    # Tạo restore script
    restore_script = f"""@echo off
chcp 65001 >nul
echo.
echo ============================================================================
echo 🔄 RESTORE Enhanced TikTok Bypass Tool
echo ============================================================================
echo Backup created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
echo.

echo 📁 Restoring files...
xcopy /E /I /Y *.py ..\\
xcopy /E /I /Y *.txt ..\\
xcopy /E /I /Y *.md ..\\

echo 📂 Restoring directories...
if exist ffmpeg xcopy /E /I /Y ffmpeg ..\\ffmpeg\\
if exist _processed xcopy /E /I /Y _processed ..\\_processed\\
if exist backup_manual xcopy /E /I /Y backup_manual ..\\backup_manual\\

echo.
echo ✅ Restore completed!
echo 🚀 Run setup_after_windows_install.bat to reinstall dependencies
echo.
pause
"""
    
    with open(os.path.join(backup_dir, "restore.bat"), "w", encoding="utf-8") as f:
        f.write(restore_script)
    
    # Tạo ZIP archive
    print(f"\n📦 Creating ZIP archive...")
    zip_filename = f"TikTok_Bypass_Tool_Backup_{timestamp}.zip"
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backup_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"   ✅ {zip_filename}")
        
        # Xóa thư mục backup tạm
        shutil.rmtree(backup_dir)
        
    except Exception as e:
        print(f"   ❌ Failed to create ZIP: {e}")
        print(f"   📁 Backup folder: {backup_dir}")
    
    print(f"\n✅ Backup completed!")
    print(f"📦 Backup file: {zip_filename}")
    print(f"📊 Files backed up: {len(files_backed_up)}")
    print(f"📂 Directories backed up: {len(dirs_backed_up)}")
    
    print(f"\n🔄 To restore after Windows install:")
    print(f"   1. Extract {zip_filename}")
    print(f"   2. Run restore.bat")
    print(f"   3. Run setup_after_windows_install.bat")
    
    return zip_filename

if __name__ == "__main__":
    try:
        backup_file = create_backup()
        print(f"\n🎯 Backup saved as: {backup_file}")
        input("\nPress Enter to exit...")
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        input("Press Enter to exit...")
