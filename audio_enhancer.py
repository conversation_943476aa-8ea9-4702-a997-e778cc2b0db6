#!/usr/bin/env python3
"""
Audio Enhancement System
Cải thiện âm thanh: noise reduction, voice enhancement, auto-ducking
"""

import subprocess
import os
import numpy as np
from typing import Dict, List, Tuple

class AudioEnhancer:
    def __init__(self):
        self.ffmpeg_path = None
        self.temp_dir = "temp_audio"
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def set_ffmpeg_path(self, path):
        """<PERSON>hi<PERSON><PERSON> lập đường dẫn FFmpeg"""
        self.ffmpeg_path = path
    
    def enhance_audio(self, input_path, output_path, enhancement_type, settings,
                     progress_callback=None, status_callback=None):
        """Cải thiện âm thanh"""
        try:
            if status_callback:
                status_callback(f"🎧 Áp dụng {enhancement_type}...")
            
            if enhancement_type == "noise_reduction":
                return self._apply_noise_reduction(input_path, output_path, settings, progress_callback)
            elif enhancement_type == "voice_enhancement":
                return self._apply_voice_enhancement(input_path, output_path, settings, progress_callback)
            elif enhancement_type == "auto_ducking":
                return self._apply_auto_ducking(input_path, output_path, settings, progress_callback)
            elif enhancement_type == "normalization":
                return self._apply_normalization(input_path, output_path, settings, progress_callback)
            elif enhancement_type == "bass_boost":
                return self._apply_bass_boost(input_path, output_path, settings, progress_callback)
            elif enhancement_type == "vocal_isolation":
                return self._apply_vocal_isolation(input_path, output_path, settings, progress_callback)
            else:
                return False, f"Unknown audio enhancement: {enhancement_type}"
                
        except Exception as e:
            return False, f"Audio enhancement error: {str(e)}"
    
    def _apply_noise_reduction(self, input_path, output_path, settings, progress_callback):
        """Giảm tiếng ồn"""
        reduction_level = settings.get('level', 0.5)  # 0.0 - 1.0
        
        # FFmpeg noise reduction using afftdn filter
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"afftdn=nr={reduction_level*20}:nf=-25:tn=1",
            "-c:v", "copy",  # Keep video unchanged
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_voice_enhancement(self, input_path, output_path, settings, progress_callback):
        """Cải thiện giọng nói"""
        clarity = settings.get('clarity', 1.2)
        bass_cut = settings.get('bass_cut', 80)  # Hz
        treble_boost = settings.get('treble_boost', 3)  # dB
        
        # Voice enhancement: high-pass filter + treble boost + compression
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"""
            highpass=f={bass_cut},
            treble=g={treble_boost}:f=8000:w=1,
            compand=attacks=0.3:decays=0.8:points=-80/-80|-45/-15|-27/-9|0/-7|20/-7:soft-knee=6:gain=5:volume=-90:delay=0.2
            """.replace('\n', '').replace(' ', ''),
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_auto_ducking(self, input_path, output_path, settings, progress_callback):
        """Auto-ducking: giảm nhạc nền khi có tiếng nói"""
        threshold = settings.get('threshold', -20)  # dB
        ratio = settings.get('ratio', 4)
        attack = settings.get('attack', 0.1)  # seconds
        release = settings.get('release', 0.5)  # seconds
        
        # Simple ducking using compand
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"compand=attacks={attack}:decays={release}:points={threshold}/{threshold}|{threshold-10}/{threshold-30}:soft-knee=6",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_normalization(self, input_path, output_path, settings, progress_callback):
        """Chuẩn hóa âm thanh"""
        target_level = settings.get('target_level', -16)  # LUFS
        
        # Two-pass normalization
        # Pass 1: Analyze
        cmd1 = [
            self.ffmpeg_path, "-i", input_path,
            "-af", "loudnorm=I=-16:TP=-1.5:LRA=11:print_format=summary",
            "-f", "null", "-"
        ]
        
        # For simplicity, use single-pass normalization
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"loudnorm=I={target_level}:TP=-1.5:LRA=11",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_bass_boost(self, input_path, output_path, settings, progress_callback):
        """Tăng bass"""
        boost_db = settings.get('boost_db', 6)
        frequency = settings.get('frequency', 100)  # Hz
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"bass=g={boost_db}:f={frequency}:w=1",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_vocal_isolation(self, input_path, output_path, settings, progress_callback):
        """Tách/loại bỏ giọng nói"""
        mode = settings.get('mode', 'isolate')  # 'isolate' or 'remove'
        
        if mode == 'isolate':
            # Vocal isolation (center channel extraction)
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-af", "pan=mono|c0=0.5*c0+0.5*c1",
                "-c:v", "copy",
                "-y", output_path
            ]
        else:  # remove
            # Vocal removal (karaoke effect)
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-af", "pan=stereo|c0=0.5*c0+-0.5*c1|c1=0.5*c1+-0.5*c0",
                "-c:v", "copy",
                "-y", output_path
            ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def add_background_music(self, video_path, music_path, output_path, settings,
                           progress_callback=None, status_callback=None):
        """Thêm nhạc nền"""
        try:
            if status_callback:
                status_callback("🎶 Thêm nhạc nền...")
            
            volume = settings.get('volume', 0.3)  # 0.0 - 1.0
            fade_in = settings.get('fade_in', 2)  # seconds
            fade_out = settings.get('fade_out', 2)  # seconds
            loop_music = settings.get('loop', True)
            
            # Get video duration
            duration_cmd = [
                self.ffmpeg_path, "-i", video_path,
                "-hide_banner", "-f", "null", "-"
            ]
            
            # Build audio mixing command
            if loop_music:
                # Loop music to match video duration
                cmd = [
                    self.ffmpeg_path,
                    "-i", video_path,  # Video with original audio
                    "-stream_loop", "-1", "-i", music_path,  # Looped music
                    "-filter_complex", f"""
                    [1:a]volume={volume},afade=t=in:st=0:d={fade_in},afade=t=out:st=0:d={fade_out}[music];
                    [0:a][music]amix=inputs=2:duration=first:dropout_transition=2[audio]
                    """.replace('\n', '').replace(' ', ''),
                    "-map", "0:v", "-map", "[audio]",
                    "-c:v", "copy", "-c:a", "aac",
                    "-shortest",  # Stop when video ends
                    "-y", output_path
                ]
            else:
                # Use music as-is
                cmd = [
                    self.ffmpeg_path,
                    "-i", video_path,
                    "-i", music_path,
                    "-filter_complex", f"""
                    [1:a]volume={volume},afade=t=in:st=0:d={fade_in},afade=t=out:st=0:d={fade_out}[music];
                    [0:a][music]amix=inputs=2:duration=first:dropout_transition=2[audio]
                    """.replace('\n', '').replace(' ', ''),
                    "-map", "0:v", "-map", "[audio]",
                    "-c:v", "copy", "-c:a", "aac",
                    "-y", output_path
                ]
            
            return self._run_ffmpeg_command(cmd, progress_callback)
            
        except Exception as e:
            return False, f"Background music error: {str(e)}"
    
    def apply_audio_effects(self, input_path, output_path, effect_type, settings,
                          progress_callback=None, status_callback=None):
        """Áp dụng hiệu ứng âm thanh"""
        try:
            if status_callback:
                status_callback(f"🎵 Áp dụng {effect_type}...")
            
            if effect_type == "reverb":
                return self._apply_reverb(input_path, output_path, settings, progress_callback)
            elif effect_type == "echo":
                return self._apply_echo(input_path, output_path, settings, progress_callback)
            elif effect_type == "pitch_shift":
                return self._apply_pitch_shift(input_path, output_path, settings, progress_callback)
            elif effect_type == "chorus":
                return self._apply_chorus(input_path, output_path, settings, progress_callback)
            else:
                return False, f"Unknown audio effect: {effect_type}"
                
        except Exception as e:
            return False, f"Audio effect error: {str(e)}"
    
    def _apply_reverb(self, input_path, output_path, settings, progress_callback):
        """Hiệu ứng reverb"""
        room_size = settings.get('room_size', 0.5)  # 0.0 - 1.0
        damping = settings.get('damping', 0.5)
        wet_level = settings.get('wet_level', 0.3)
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"afreqshift=shift=0,aecho=0.8:0.9:{int(room_size*1000)}:{wet_level}",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_echo(self, input_path, output_path, settings, progress_callback):
        """Hiệu ứng echo"""
        delay = settings.get('delay', 0.5)  # seconds
        decay = settings.get('decay', 0.6)
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"aecho=0.8:0.9:{int(delay*1000)}:{decay}",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_pitch_shift(self, input_path, output_path, settings, progress_callback):
        """Thay đổi pitch"""
        semitones = settings.get('semitones', 0)  # -12 to +12
        
        # Convert semitones to ratio
        ratio = 2 ** (semitones / 12.0)
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-af", f"asetrate=44100*{ratio},aresample=44100",
            "-c:v", "copy",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _run_ffmpeg_command(self, cmd, progress_callback):
        """Chạy lệnh FFmpeg"""
        try:
            import platform
            
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                creationflags = 0x08000000 | 0x00000008
            
            process = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, timeout=600, startupinfo=startupinfo, creationflags=creationflags
            )
            
            if progress_callback:
                progress_callback(100)
            
            return process.returncode == 0, process.stderr if process.returncode != 0 else "Success"
            
        except Exception as e:
            return False, str(e)

# Audio presets
AUDIO_PRESETS = {
    "podcast_voice": {
        "type": "voice_enhancement",
        "settings": {"clarity": 1.3, "bass_cut": 100, "treble_boost": 4}
    },
    "music_clarity": {
        "type": "normalization", 
        "settings": {"target_level": -14}
    },
    "bass_heavy": {
        "type": "bass_boost",
        "settings": {"boost_db": 8, "frequency": 80}
    },
    "clean_audio": {
        "type": "noise_reduction",
        "settings": {"level": 0.7}
    },
    "karaoke": {
        "type": "vocal_isolation",
        "settings": {"mode": "remove"}
    }
}

# Test function
if __name__ == "__main__":
    enhancer = AudioEnhancer()
    print("🎵 Audio Enhancement System initialized")
    print("Available presets:", list(AUDIO_PRESETS.keys()))
