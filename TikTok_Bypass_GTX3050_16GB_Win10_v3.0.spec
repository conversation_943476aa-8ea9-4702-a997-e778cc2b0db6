# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('ffmpeg.exe', '.'), ('ffmpeg', 'ffmpeg'), ('advanced_grid_processor.py', '.'), ('video_processor.py', '.'), ('ui_config.py', '.'), ('ai_smart_enhancer_safe.py', '.'), ('audio_enhancer.py', '.'), ('advanced_effects.py', '.'), ('utils.py', '.'), ('wink_style_enhancer.py', '.')]
binaries = []
hiddenimports = ['PyQt5.QtWidgets', 'PyQt5.QtCore', 'PyQt5.QtGui', 'cv2', 'numpy', 'concurrent.futures', 'threading', 'subprocess', 'platform']
tmp_ret = collect_all('PyQt5')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['launch_complete.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'scipy', 'pandas', 'jupyter', 'IPython', 'tkinter', 'torch', 'torchvision', 'realesrgan', 'basicsr'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='TikTok_Bypass_GTX3050_16GB_Win10_v3.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
