#!/usr/bin/env python3
"""
🔍 DEBUG FFmpeg Commands
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ffmpeg_command():
    """Test FFmpeg command generation"""
    print("🔍 TESTING FFMPEG COMMAND GENERATION...")
    
    try:
        from video_processor import VideoProcessor
        
        processor = VideoProcessor()
        
        # Test effects
        effects = {
            'use_gpu': True,
            'blur': True,
            'shift': True
        }
        
        # Test command generation
        input_path = "test_input.mp4"
        output_path = "test_output.mp4"
        zoom_percent = 110
        
        cmd = processor.build_ffmpeg_command(input_path, output_path, effects, zoom_percent)
        
        print("📊 Generated FFmpeg Command:")
        print(" ".join(cmd))
        
        # Check for GPU encoder
        gpu_encoder_found = False
        cpu_threads_found = False
        
        for i, arg in enumerate(cmd):
            if arg in ['-c:v', '-codec:v']:
                if i + 1 < len(cmd):
                    encoder = cmd[i + 1]
                    print(f"📊 Video Encoder: {encoder}")
                    if 'nvenc' in encoder or 'amf' in encoder or 'qsv' in encoder:
                        gpu_encoder_found = True
                        print("✅ GPU Encoder Detected!")
                    else:
                        print("⚠️ CPU Encoder Detected!")
            
            if arg == '-threads':
                if i + 1 < len(cmd):
                    threads = cmd[i + 1]
                    print(f"📊 FFmpeg Threads: {threads}")
                    cpu_threads_found = True
        
        if not gpu_encoder_found:
            print("❌ NO GPU ENCODER IN COMMAND!")
        
        if cpu_threads_found:
            print("⚠️ FFmpeg using CPU threads - this might cause CPU overload!")
        
        return cmd
        
    except Exception as e:
        print(f"❌ FFmpeg command test error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_gpu_encoder_selection():
    """Test GPU encoder selection logic"""
    print("\n🔍 TESTING GPU ENCODER SELECTION...")
    
    try:
        from video_processor import VideoProcessor
        
        processor = VideoProcessor()
        
        # Test with GPU enabled
        effects_gpu = {'use_gpu': True}
        encoder, gpu_type = processor.get_optimal_encoder()
        
        print(f"📊 GPU Enabled - Encoder: {encoder}, Type: {gpu_type}")
        
        # Check if GPU encoder is actually used in command
        cmd = processor.build_ffmpeg_command("test.mp4", "out.mp4", effects_gpu)
        
        gpu_used_in_cmd = False
        for i, arg in enumerate(cmd):
            if arg in ['-c:v', '-codec:v'] and i + 1 < len(cmd):
                actual_encoder = cmd[i + 1]
                print(f"📊 Actual encoder in command: {actual_encoder}")
                if actual_encoder == encoder:
                    gpu_used_in_cmd = True
                    print("✅ GPU encoder correctly used in command!")
                else:
                    print(f"❌ Mismatch! Expected {encoder}, got {actual_encoder}")
        
        if not gpu_used_in_cmd:
            print("❌ GPU encoder NOT found in command!")
        
        return encoder, gpu_type
        
    except Exception as e:
        print(f"❌ GPU encoder test error: {e}")
        return None, None

def main():
    """Main debug function"""
    print("🚀 STARTING FFMPEG DEBUG...")
    print("=" * 60)
    
    # Test FFmpeg command generation
    cmd = test_ffmpeg_command()
    
    # Test GPU encoder selection
    encoder, gpu_type = test_gpu_encoder_selection()
    
    print("\n" + "=" * 60)
    print("📋 FFMPEG DEBUG SUMMARY:")
    print("=" * 60)
    
    if cmd:
        print("✅ FFmpeg command generation: SUCCESS")
    else:
        print("❌ FFmpeg command generation: FAILED")
    
    if encoder and 'nvenc' in encoder:
        print("✅ GPU encoder selection: SUCCESS")
    else:
        print("❌ GPU encoder selection: FAILED or CPU fallback")
    
    print("\n🎯 POTENTIAL CPU OVERLOAD CAUSES:")
    print("1. FFmpeg using CPU encoder instead of GPU")
    print("2. FFmpeg -threads parameter too high")
    print("3. Complex video filters causing CPU bottleneck")
    print("4. Multiple FFmpeg processes running simultaneously")

if __name__ == "__main__":
    main()
