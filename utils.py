import os

def get_ffmpeg_path():
    # Ưu tiên dùng ffmpeg trong thư mục hiện tại nếu có
    ffmpeg_local = os.path.join(os.getcwd(), "ffmpeg", "ffmpeg.exe")
    if os.path.exists(ffmpeg_local):
        return ffmpeg_local
    return "ffmpeg"  # fallback: dùng ffmpeg từ hệ thống

def generate_output_path(input_path):
    import datetime
    import random
    import string
    name, ext = os.path.splitext(os.path.basename(input_path))
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    rand = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return os.path.join("output_videos", f"{name}_bypassed_{timestamp}_{rand}{ext}")
