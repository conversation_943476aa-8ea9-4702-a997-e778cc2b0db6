# ============================================================================
# ENHANCED TIKTOK BYPASS TOOL - REQUIREMENTS
# ============================================================================
# Version: 3.0 Complete
# Compatible with: Python 3.8+, Windows 10/11
# Last updated: 2025-01-23

# ============================================================================
# CORE GUI & VIDEO PROCESSING (REQUIRED)
# ============================================================================
PyQt5==5.15.11
PyQt5-Qt5==5.15.2
PyQt5-sip==12.17.0
opencv-python==*********
numpy==2.2.6
Pillow==11.2.1

# ============================================================================
# SYSTEM & UTILITIES (REQUIRED)
# ============================================================================
psutil==7.0.0
tqdm==4.67.1
requests==2.32.4
urllib3==2.4.0

# ============================================================================
# AUDIO PROCESSING (REQUIRED for audio effects)
# ============================================================================
librosa==0.10.2
soundfile==0.12.1
audioread==3.0.1
scipy==1.14.1

# ============================================================================
# GPU ACCELERATION (OPTIONAL - for better performance)
# ============================================================================
# Uncomment below if you have NVIDIA GPU and want GPU acceleration
# torch==2.7.1+cu121 --index-url https://download.pytorch.org/whl/cu121
# torchvision==0.22.1+cu121 --index-url https://download.pytorch.org/whl/cu121

# For CPU-only (lighter installation):
# torch==2.7.1+cpu --index-url https://download.pytorch.org/whl/cpu
# torchvision==0.22.1+cpu --index-url https://download.pytorch.org/whl/cpu

# ============================================================================
# AI UPSCALING (OPTIONAL - can be disabled in settings)
# ============================================================================
# Note: These packages may have compatibility issues
# Install manually if needed: pip install realesrgan
# realesrgan==0.3.0

# ============================================================================
# DEVELOPMENT & DEBUGGING (OPTIONAL)
# ============================================================================
# Uncomment for development/debugging
# pytest==8.3.4
# black==24.10.0
# flake8==7.1.1

# ============================================================================
# ADDITIONAL CODECS & FORMATS (OPTIONAL)
# ============================================================================
# For additional video format support
# ffmpeg-python==0.2.0
# moviepy==1.0.3

# ============================================================================
# INSTALLATION NOTES:
# ============================================================================
# 1. Install core requirements: pip install -r requirements.txt
# 2. For GPU support: Uncomment torch lines above
# 3. For AI upscaling: Install realesrgan manually if needed
# 4. FFmpeg binary is included in the project folder
# ============================================================================
