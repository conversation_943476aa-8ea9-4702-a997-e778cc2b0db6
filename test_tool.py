#!/usr/bin/env python3
"""
Test Tool - <PERSON><PERSON><PERSON> tra tất cả chức năng của Enhanced TikTok Bypass Tool
"""

import os
import sys
import time
import traceback
from datetime import datetime

def test_imports():
    """Test import các modules cần thiết"""
    print("🧪 Testing imports...")
    
    tests = [
        ("PyQt5.QtWidgets", "PyQt5 GUI framework"),
        ("PyQt5.QtCore", "PyQt5 Core"),
        ("PyQt5.QtGui", "PyQt5 GUI components"),
        ("cv2", "OpenCV for video processing"),
        ("numpy", "NumPy for array operations"),
        ("PIL", "Pillow for image processing"),
        ("psutil", "System utilities"),
        ("tqdm", "Progress bars"),
        ("requests", "HTTP requests")
    ]
    
    results = []
    
    for module, description in tests:
        try:
            __import__(module)
            print(f"   ✅ {module:<20} - {description}")
            results.append((module, True, None))
        except ImportError as e:
            print(f"   ❌ {module:<20} - {description} (Error: {e})")
            results.append((module, False, str(e)))
    
    return results

def test_project_files():
    """Test sự tồn tại của các files quan trọng"""
    print("\n📁 Testing project files...")
    
    required_files = [
        ("launch_complete.py", "Main launcher"),
        ("enhanced_ui_complete.py", "Enhanced UI"),
        ("requirements.txt", "Dependencies list"),
        ("video_processor.py", "Video processing"),
        ("ui_config.py", "UI configuration")
    ]
    
    optional_files = [
        ("ffmpeg.exe", "FFmpeg binary"),
        ("advanced_grid_processor.py", "Grid processor"),
        ("ai_smart_enhancer_safe.py", "AI enhancer"),
        ("utils.py", "Utilities")
    ]
    
    results = []
    
    print("   Required files:")
    for file, description in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file:<25} - {description} ({size:,} bytes)")
            results.append((file, True, size))
        else:
            print(f"   ❌ {file:<25} - {description} (Missing)")
            results.append((file, False, 0))
    
    print("   Optional files:")
    for file, description in optional_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file:<25} - {description} ({size:,} bytes)")
            results.append((file, True, size))
        else:
            print(f"   ⚠️ {file:<25} - {description} (Optional)")
            results.append((file, False, 0))
    
    return results

def test_ui_creation():
    """Test tạo UI (không hiển thị)"""
    print("\n🎨 Testing UI creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # Tạo QApplication nếu chưa có
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        
        # Test import UI
        from enhanced_ui_complete import EnhancedCompleteUI
        print("   ✅ Enhanced UI import successful")
        
        # Test tạo UI (không show)
        ui = EnhancedCompleteUI()
        print("   ✅ Enhanced UI creation successful")
        
        # Test một số properties cơ bản
        if hasattr(ui, 'video_paths'):
            print("   ✅ Video paths attribute exists")
        
        if hasattr(ui, 'init_ui'):
            print("   ✅ UI initialization method exists")
        
        return True, "UI creation successful"
        
    except Exception as e:
        error_msg = f"UI creation failed: {str(e)}"
        print(f"   ❌ {error_msg}")
        return False, error_msg

def test_video_processor():
    """Test video processor import"""
    print("\n🎬 Testing video processor...")
    
    try:
        from video_processor import VideoProcessor
        print("   ✅ VideoProcessor import successful")
        
        # Test tạo instance
        processor = VideoProcessor()
        print("   ✅ VideoProcessor creation successful")
        
        return True, "Video processor OK"
        
    except Exception as e:
        error_msg = f"Video processor failed: {str(e)}"
        print(f"   ❌ {error_msg}")
        return False, error_msg

def generate_report(import_results, file_results, ui_result, processor_result):
    """Tạo báo cáo test"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test_report_{timestamp}.txt"
    
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("=" * 80 + "\n")
        f.write("ENHANCED TIKTOK BYPASS TOOL - TEST REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Python Version: {sys.version}\n")
        f.write(f"Platform: {sys.platform}\n\n")
        
        # Import results
        f.write("IMPORT TESTS:\n")
        f.write("-" * 40 + "\n")
        passed_imports = sum(1 for _, success, _ in import_results if success)
        total_imports = len(import_results)
        f.write(f"Passed: {passed_imports}/{total_imports}\n\n")
        
        for module, success, error in import_results:
            status = "✅ PASS" if success else "❌ FAIL"
            f.write(f"{status} {module}\n")
            if error:
                f.write(f"      Error: {error}\n")
        
        # File results
        f.write("\nFILE TESTS:\n")
        f.write("-" * 40 + "\n")
        existing_files = sum(1 for _, exists, _ in file_results if exists)
        total_files = len(file_results)
        f.write(f"Found: {existing_files}/{total_files}\n\n")
        
        for file, exists, size in file_results:
            status = "✅ EXISTS" if exists else "❌ MISSING"
            f.write(f"{status} {file}")
            if exists and size > 0:
                f.write(f" ({size:,} bytes)")
            f.write("\n")
        
        # UI test
        f.write("\nUI TEST:\n")
        f.write("-" * 40 + "\n")
        ui_success, ui_msg = ui_result
        status = "✅ PASS" if ui_success else "❌ FAIL"
        f.write(f"{status} {ui_msg}\n")
        
        # Processor test
        f.write("\nVIDEO PROCESSOR TEST:\n")
        f.write("-" * 40 + "\n")
        proc_success, proc_msg = processor_result
        status = "✅ PASS" if proc_success else "❌ FAIL"
        f.write(f"{status} {proc_msg}\n")
        
        # Summary
        f.write("\nSUMMARY:\n")
        f.write("-" * 40 + "\n")
        total_tests = total_imports + total_files + 2  # +2 for UI and processor
        passed_tests = passed_imports + existing_files + (1 if ui_success else 0) + (1 if proc_success else 0)
        f.write(f"Total Tests: {total_tests}\n")
        f.write(f"Passed: {passed_tests}\n")
        f.write(f"Failed: {total_tests - passed_tests}\n")
        f.write(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%\n")
    
    return report_file

def main():
    """Main test function"""
    print("=" * 80)
    print("🧪 ENHANCED TIKTOK BYPASS TOOL - COMPREHENSIVE TEST")
    print("=" * 80)
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python: {sys.version}")
    print(f"Platform: {sys.platform}")
    print()
    
    try:
        # Run tests
        import_results = test_imports()
        file_results = test_project_files()
        ui_result = test_ui_creation()
        processor_result = test_video_processor()
        
        # Generate report
        print(f"\n📊 Generating test report...")
        report_file = generate_report(import_results, file_results, ui_result, processor_result)
        print(f"   ✅ Report saved: {report_file}")
        
        # Summary
        print(f"\n" + "=" * 80)
        print("📋 TEST SUMMARY")
        print("=" * 80)
        
        import_passed = sum(1 for _, success, _ in import_results if success)
        files_found = sum(1 for _, exists, _ in file_results if exists)
        ui_passed = 1 if ui_result[0] else 0
        proc_passed = 1 if processor_result[0] else 0
        
        total_tests = len(import_results) + len(file_results) + 2
        total_passed = import_passed + files_found + ui_passed + proc_passed
        
        print(f"📦 Imports: {import_passed}/{len(import_results)} passed")
        print(f"📁 Files: {files_found}/{len(file_results)} found")
        print(f"🎨 UI Test: {'✅ PASS' if ui_result[0] else '❌ FAIL'}")
        print(f"🎬 Processor: {'✅ PASS' if processor_result[0] else '❌ FAIL'}")
        print(f"📊 Overall: {total_passed}/{total_tests} ({(total_passed/total_tests)*100:.1f}%)")
        
        if total_passed == total_tests:
            print(f"\n🎉 ALL TESTS PASSED! Tool is ready to use.")
        else:
            print(f"\n⚠️ Some tests failed. Check the report for details.")
        
        print(f"\n🚀 To launch tool: python launch_complete.py")
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        print(f"Traceback:")
        traceback.print_exc()
    
    print(f"\nTest completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
