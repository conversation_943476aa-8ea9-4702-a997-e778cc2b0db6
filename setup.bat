@echo off
chcp 65001 >nul
title TikTok Bypass Tool - Environment Setup

echo.
echo 🚀 TikTok Bypass Tool - Environment Setup
echo ==========================================
echo.

REM Kiểm tra Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python không được tìm thấy!
    echo 📥 Vui lòng cài đặt Python từ: https://www.python.org/downloads/
    echo ⚠️  Nhớ check "Add Python to PATH" khi cài đặt
    pause
    exit /b 1
)

echo ✅ Python đã được tìm thấy
python --version

echo.
echo 🔄 Đang chạy setup script...
echo.

python setup_environment.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Setup thất bại!
    echo 💡 Thử chạy lại với quyền Administrator
    pause
    exit /b 1
)

echo.
echo 🎉 Setup hoàn thành!
echo 📝 Bạn có thể chạy tool bằng: python main.py
echo.
pause
