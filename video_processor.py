import os
import subprocess
import platform
import tempfile
import shutil
from advanced_grid_processor import AdvancedGridProcessor

class FFmpegManager:
    def __init__(self):
        self.ffmpeg_path = None
        self.find_ffmpeg()

    def find_ffmpeg(self):
        # Fix cho EXE build - tìm FFmpeg trong bundle
        import sys

        # Detect if running as EXE
        if getattr(sys, 'frozen', False):
            # Running as EXE
            exe_dir = os.path.dirname(sys.executable)
            search_paths = [
                os.path.join(exe_dir, "ffmpeg.exe"),
                os.path.join(exe_dir, "ffmpeg", "ffmpeg.exe"),
                os.path.join(exe_dir, "ffmpeg", "bin", "ffmpeg.exe"),
                os.path.join(exe_dir, "_internal", "ffmpeg.exe"),
                os.path.join(exe_dir, "_internal", "ffmpeg", "ffmpeg.exe"),
            ]
        else:
            # Running as Python script
            search_paths = [
                os.path.join(os.getcwd(), "ffmpeg", "ffmpeg.exe"),
                os.path.join(os.getcwd(), "ffmpeg.exe"),
                os.path.join(os.getcwd(), "ffmpeg", "bin", "ffmpeg.exe"),
            ]

        # Add common system paths
        search_paths.extend([
            "ffmpeg.exe",  # Trong PATH
            "ffmpeg",      # Linux/Mac
            "C:\\ffmpeg\\bin\\ffmpeg.exe",  # Đường dẫn phổ biến
            "C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe"
        ])

        for path in search_paths:
            if self._test_ffmpeg_path(path):
                self.ffmpeg_path = path
                print(f"✅ Found FFmpeg at: {path}")
                return path

        print("❌ FFmpeg not found in any location!")
        return None

    def _test_ffmpeg_path(self, path):
        try:
            # Hide CMD window completely
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            result = subprocess.run([path, "-version"],
                                  capture_output=True, text=True, timeout=5,
                                  startupinfo=startupinfo, creationflags=creationflags)
            return result.returncode == 0
        except:
            return False

    def get_path(self):
        return self.ffmpeg_path

    def is_available(self):
        return self.ffmpeg_path is not None

class VideoProcessor:
    def __init__(self):
        self.ffmpeg_manager = FFmpegManager()
        self.advanced_grid_processor = AdvancedGridProcessor()

    def generate_safe_filename(self, input_path, suffix="processed", gpt_title=None):
        base = gpt_title if gpt_title else os.path.splitext(os.path.basename(input_path))[0]
        base = base.replace(" ", "_").lower()

        parent_folder = os.path.basename(os.path.dirname(input_path))
        output_dir = os.path.join(os.path.dirname(input_path), f"{parent_folder}_processed")
        os.makedirs(output_dir, exist_ok=True)

        filename = f"{base}_{suffix}.mp4"
        return os.path.join(output_dir, filename)

    def check_gpu_support(self):
        """🚀 Enhanced GPU detection with capability analysis - GTX 3050 optimized"""
        try:
            # Skip if FFmpeg not available
            if not self.ffmpeg_manager.is_available():
                return False, []

            # Hide CMD window completely
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            result = subprocess.run(
                [self.ffmpeg_manager.get_path(), "-encoders"],
                capture_output=True, text=True, timeout=15,  # Increased timeout
                startupinfo=startupinfo, creationflags=creationflags
            )

            # Check for multiple GPU encoder types
            gpu_encoders = []

            # GTX 3050 should support NVENC
            if "h264_nvenc" in result.stdout or "hevc_nvenc" in result.stdout:
                gpu_encoders.append("nvidia")
                print("✅ NVIDIA GPU encoder detected (GTX 3050 compatible)")

            if "h264_amf" in result.stdout:
                gpu_encoders.append("amd")

            if "h264_qsv" in result.stdout:
                gpu_encoders.append("intel")

            return len(gpu_encoders) > 0, gpu_encoders
        except Exception as e:
            print(f"⚠️ GPU detection failed: {e}")
            return False, []

    def get_optimal_encoder(self):
        """Get the best available encoder for current system"""
        has_gpu, gpu_types = self.check_gpu_support()

        if has_gpu:
            # Priority: NVIDIA > AMD > Intel
            if "nvidia" in gpu_types:
                return "h264_nvenc", "nvidia"
            elif "amd" in gpu_types:
                return "h264_amf", "amd"
            elif "intel" in gpu_types:
                return "h264_qsv", "intel"

        return "libx264", "cpu"

    def get_optimal_threads(self):
        """🧠 Calculate optimal thread count based on system resources"""
        try:
            import psutil
            cpu_count = psutil.cpu_count(logical=False)  # Physical cores
            logical_count = psutil.cpu_count(logical=True)  # Logical cores
            memory_gb = psutil.virtual_memory().total / (1024**3)

            # Conservative thread calculation for video processing
            if memory_gb >= 16:
                return min(logical_count, 8)  # Max 8 threads for high memory
            elif memory_gb >= 8:
                return min(logical_count, 6)  # Max 6 threads for medium memory
            else:
                return min(logical_count, 4)  # Max 4 threads for low memory
        except ImportError:
            # Fallback without psutil
            import os
            cpu_count = os.cpu_count() or 4
            # Conservative fallback
            if cpu_count >= 8:
                return 6
            elif cpu_count >= 4:
                return 4
            else:
                return 2

    def build_ffmpeg_command(self, input_path, output_path, effects, zoom_percent=110):
        """🚀 Build optimized FFmpeg command with performance enhancements"""
        cmd = [self.ffmpeg_manager.get_path(), "-y"]

        # 🚀 INTELLIGENT THREAD OPTIMIZATION
        use_gpu = effects.get('use_gpu', False)
        if use_gpu:
            # GPU encoding: Use fewer CPU threads to avoid overload
            cmd += ["-threads", "2"]  # Minimal CPU threads for GPU encoding
        else:
            # CPU encoding: Use optimal threads
            cmd += ["-threads", str(self.get_optimal_threads())]

        cmd += ["-i", input_path]
        vf = []

        # Zoom
        if zoom_percent > 100:
            scale = zoom_percent / 100
            vf.append(f"scale=iw*{scale}:ih*{scale}:flags=lanczos")
            vf.append(f"crop=iw/{scale}:ih/{scale}:(iw-iw/{scale})/2:(ih-ih/{scale})/2")

        # 🌈 ADVANCED COLOR BYPASS (DYNAMIC)
        if effects.get("color_shift"):
            import random

            # Dynamic color space manipulation
            brightness = random.uniform(-0.05, 0.05)
            contrast = random.uniform(0.95, 1.05)
            saturation = random.uniform(0.95, 1.05)
            gamma = random.uniform(0.95, 1.05)
            hue_shift = random.uniform(-5, 5)

            # Advanced color filters
            vf.append(f"eq=brightness={brightness}:contrast={contrast}:saturation={saturation}:gamma={gamma}")

            # Hue shifting
            if abs(hue_shift) > 1:
                vf.append(f"hue=h={hue_shift}")

            # Color temperature variation
            temp_shift = random.uniform(-100, 100)
            if abs(temp_shift) > 30:
                if temp_shift > 0:  # Warmer
                    vf.append("colorbalance=rs=0.05:gs=0.02:bs=-0.05")
                else:  # Cooler
                    vf.append("colorbalance=rs=-0.05:gs=-0.02:bs=0.05")

            # Subtle noise injection (anti-AI)
            noise_strength = random.randint(3, 8)
            vf.append(f"noise=alls={noise_strength}:allf=t")

        # 📐 ADVANCED GEOMETRIC BYPASS
        if effects.get("geometric_bypass", True):  # Auto-enabled
            import random

            # Micro geometric transformations
            rotation = random.uniform(-0.2, 0.2)  # Micro rotation
            scale_var = random.uniform(0.999, 1.001)  # Micro scaling

            # Apply micro rotation
            if abs(rotation) > 0.05:
                vf.append(f"rotate={rotation}*PI/180:fillcolor=black:bilinear=0")

            # Apply micro scaling with crop back
            if abs(scale_var - 1.0) > 0.0005:
                vf.append(f"scale=iw*{scale_var}:ih*{scale_var}:flags=lanczos")
                vf.append("crop=iw/1.001:ih/1.001:(iw-iw/1.001)/2:(ih-ih/1.001)/2")

        if effects.get("blur"):
            # 🔍 ADVANCED BLUR BYPASS (DYNAMIC)
            import random
            blur_strength = random.uniform(0.5, 1.5)
            blur_type = random.choice(["boxblur", "gblur"])

            if blur_type == "boxblur":
                vf.append(f"boxblur={blur_strength}:{blur_strength}")
            else:
                vf.append(f"gblur=sigma={blur_strength}")

            # Add subtle sharpening to counteract
            sharpen = random.uniform(0.1, 0.3)
            vf.append(f"unsharp=5:5:{sharpen}:5:5:{sharpen/2}")

        if effects.get("grid_config"):
            grid = effects["grid_config"]

            # Check if using advanced grid features
            if (grid.get("type") in ["random_grid", "dynamic_grid", "asymmetric", "fibonacci",
                                   "hexagon", "custom_pattern", "multi_layer",
                                   # NEW COMPREHENSIVE PATTERNS
                                   "dots_pattern", "triangle_pattern", "star_pattern", "wave_pattern",
                                   "diamond_pattern", "rhombus_pattern", "spiral_pattern", "zigzag_pattern",
                                   "target_pattern", "web_pattern", "border_frame", "geometric_mix",
                                   "circus_pattern", "rainbow_arch", "snowflake_pattern", "flower_pattern",
                                   "gear_pattern", "fire_pattern"] or
                grid.get("random_position") or grid.get("animated") or
                grid.get("color") in ["rainbow", "random_color", "flashing"]):

                # Mark for advanced grid processing (will be applied later)
                effects["use_advanced_grid"] = True
            else:
                # Use standard FFmpeg grid for basic grids
                color_map = {
                    "white": "white", "red": "red", "blue": "blue",
                    "green": "green", "yellow": "yellow", "purple": "purple", "orange": "orange"
                }
                color = color_map.get(grid["color"], "white")
                opacity = grid.get("opacity", 0.8)
                line_width = grid.get("line_width", grid.get("thickness", 2))
                grid_type = grid["type"]

                if grid_type == "3x3":
                    vf.append(f"drawgrid=width=iw/3:height=ih/3:color={color}:thickness={line_width}")
                elif grid_type == "4x4":
                    vf.append(f"drawgrid=width=iw/4:height=ih/4:color={color}:thickness={line_width}")
                elif grid_type == "5x5":
                    vf.append(f"drawgrid=width=iw/5:height=ih/5:color={color}:thickness={line_width}")
                elif grid_type == "6x6":
                    vf.append(f"drawgrid=width=iw/6:height=ih/6:color={color}:thickness={line_width}")
                elif grid_type == "center_cross":
                    vf.append(f"drawbox=x=iw/2-{line_width/2}:y=0:w={line_width}:h=ih:color={color}:t=fill")
                    vf.append(f"drawbox=x=0:y=ih/2-{line_width/2}:w=iw:h={line_width}:color={color}:t=fill")
                elif grid_type == "golden_ratio":
                    vf.append(f"drawbox=x=iw*0.382-{line_width/2}:y=0:w={line_width}:h=ih:color={color}:t=fill")
                    vf.append(f"drawbox=x=iw*0.618-{line_width/2}:y=0:w={line_width}:h=ih:color={color}:t=fill")
                    vf.append(f"drawbox=x=0:y=ih*0.382-{line_width/2}:w=iw:h={line_width}:color={color}:t=fill")
                    vf.append(f"drawbox=x=0:y=ih*0.618-{line_width/2}:w=iw:h={line_width}:color={color}:t=fill")
                elif grid_type == "diagonal":
                    vf.append(f"drawbox=x=0:y=0:w=iw:h={line_width}:color={color}:t=fill")
                    vf.append(f"drawbox=x=0:y=ih-{line_width}:w=iw:h={line_width}:color={color}:t=fill")

        if effects.get("noise"):
            vf.append("noise=alls=20:allf=t+u")

        # ⏱️ ADVANCED TEMPORAL BYPASS
        if effects.get("temporal_bypass", True):  # Auto-enabled
            import random

            # Micro temporal variations
            fps_variation = random.uniform(0.998, 1.002)
            if abs(fps_variation - 1.0) > 0.0005:
                vf.append(f"fps=fps=30*{fps_variation}")

            # Subtle temporal noise
            if random.choice([True, False]):
                vf.append("noise=alls=2:allf=t")

            # Micro speed variation
            speed_var = random.uniform(0.9995, 1.0005)
            if abs(speed_var - 1.0) > 0.0001:
                vf.append(f"setpts=PTS/{speed_var}")

        # 🌟 WINK-STYLE ENHANCEMENT (PROFESSIONAL QUALITY)
        if effects.get("wink_enhancement"):
            wink_level = effects.get("wink_level", "high")
            wink_filters = self.get_wink_enhancement_filters(wink_level)
            vf.extend(wink_filters)

        if vf:
            cmd += ["-vf", ",".join(vf)]

        # 🔥 ADVANCED AUDIO BYPASS (DYNAMIC)
        if effects.get("audio"):
            # Dynamic audio bypass with randomization
            import random
            pitch_shift = random.uniform(-0.5, 0.5)
            tempo_change = random.uniform(0.99, 1.01)
            volume_adjust = random.uniform(0.98, 1.02)

            audio_filters = []

            # Pitch variation
            if abs(pitch_shift) > 0.1:
                audio_filters.append(f"asetrate=44100*{1 + pitch_shift/100}")
                audio_filters.append("aresample=44100")

            # Tempo variation
            audio_filters.append(f"atempo={tempo_change}")

            # Volume variation
            audio_filters.append(f"volume={volume_adjust}")

            # Subtle reverb
            reverb_strength = random.uniform(0.1, 0.2)
            audio_filters.append(f"aecho=0.8:0.88:{int(reverb_strength*100)}:0.3")

            # Frequency filtering
            freq_mult = random.uniform(0.99, 1.01)
            audio_filters.append(f"highpass=f={int(20*freq_mult)}")
            audio_filters.append(f"lowpass=f={int(20000*freq_mult)}")

            cmd += ["-af", ",".join(audio_filters)]
        else:
            cmd += ["-c:a", "copy"]

        # 🔍 ADVANCED METADATA BYPASS
        import random
        import time

        # Dynamic encoding parameters for fingerprint variation
        crf_value = random.randint(20, 25)
        preset = random.choice(["medium", "fast"])
        bitrate_mult = random.uniform(0.95, 1.05)

        # Random metadata
        random_title = f"Video_{random.randint(10000, 99999)}"
        random_comment = f"Processed_{int(time.time())}"

        # 🚀 INTELLIGENT ENCODER SELECTION - GPU FIRST!
        encoder, gpu_type = self.get_optimal_encoder()
        use_gpu = effects.get('use_gpu', False)

        if use_gpu and encoder != "libx264":
            # GPU encoding
            if effects.get("wink_enhancement", False):
                cmd += ["-c:v", encoder, "-preset", "fast", "-crf", "20"]
            else:
                cmd += ["-c:v", encoder, "-preset", "fast", "-crf", str(crf_value), "-pix_fmt", "yuv420p"]
        else:
            # CPU encoding (fallback)
            if effects.get("wink_enhancement", False):
                cmd += ["-c:v", "libx264", "-preset", "fast", "-crf", "20"]
            else:
                cmd += ["-c:v", "libx264", "-preset", "fast", "-crf", str(crf_value), "-pix_fmt", "yuv420p"]

        # 🔍 ADD METADATA BYPASS
        cmd += [
            "-metadata", f"title={random_title}",
            "-metadata", f"comment={random_comment}",
            "-metadata", f"creation_time={int(time.time())}",
            "-movflags", "+faststart",
            "-avoid_negative_ts", "make_zero",
            output_path
        ]
        return cmd

    def get_wink_enhancement_filters(self, level="high"):
        """🚀 OPTIMIZED Wink-style AI-LIKE enhancement - Multi-level adaptive sharpening"""

        if level == "ultra":
            # 🔥 ULTRA PERFORMANCE - Maximum quality with GPU optimization
            return [
                "unsharp=5:5:1.2:3:3:0.8",     # Strong primary sharpening
                "cas=0.15",                     # Enhanced Contrast Adaptive Sharpening
                "hqdn3d=1.5:1:2:1.5",          # Optimized noise reduction
                "eq=contrast=1.1:brightness=0.02:saturation=1.05",  # Subtle color enhancement
                "smartblur=1.5:0.35:0"         # Smart detail preservation
            ]
        elif level == "high":
            # 🌟 HIGH QUALITY - Balanced performance and quality
            return [
                "unsharp=5:5:1.0:3:3:0.6",     # Optimized primary sharpening
                "cas=0.12",                     # Moderate Contrast Adaptive Sharpening
                "hqdn3d=2:1.5:2.5:2",          # Balanced noise reduction
                "eq=contrast=1.05:saturation=1.02"  # Light color enhancement
            ]
        elif level == "medium":
            # ⚡ MEDIUM - Fast processing with good quality
            return [
                "unsharp=7:7:0.8:5:5:0.5",     # Gentle sharpening (original)
                "cas=0.08",                     # Light CAS
                "eq=contrast=1.03"             # Minimal color adjustment
            ]
        else:  # light
            # 💨 LIGHT - Maximum speed, minimal processing
            return [
                "unsharp=9:9:0.6:7:7:0.4",     # Very gentle sharpening
                "cas=0.05"                      # Minimal CAS
            ]

    def process_single_video(self, input_path, effects, zoom_percent=110, status_callback=None, progress_callback=None):
        try:
            if status_callback:
                status_callback("🔍 Kiểm tra file đầu vào...")
            if progress_callback:
                progress_callback(5)

            if not os.path.exists(input_path):
                return {"success": False, "error": f"File not found: {input_path}"}
            if not self.ffmpeg_manager.is_available():
                return {"success": False, "error": "FFmpeg not available"}

            # Process video with FFmpeg effects only (no upscaling)
            output_path = self.generate_safe_filename(input_path, "processed")

            # Build FFmpeg command with all effects
            cmd = self.build_ffmpeg_command(input_path, output_path, effects, zoom_percent)

            if status_callback:
                status_callback("🎬 Đang xử lý video với FFmpeg...")
            if progress_callback:
                progress_callback(20)

            # Execute FFmpeg command
            success = self.run_ffmpeg_command(cmd, progress_callback, status_callback)

            if success:
                # Check if need advanced grid post-processing
                if effects.get("use_advanced_grid") and effects.get("grid_config"):
                    grid_type = effects["grid_config"].get("type", "unknown")
                    grid_name_map = {
                        "random_grid": "Random Grid (Anti-Reup)",
                        "dynamic_grid": "Dynamic Moving Grid",
                        "asymmetric": "Asymmetric Grid",
                        "fibonacci": "Fibonacci Spiral",
                        "hexagon": "Hexagon Grid",
                        "custom_pattern": "Custom Pattern",
                        "multi_layer": "Multi-Layer Grid",
                        # NEW COMPREHENSIVE PATTERNS
                        "dots_pattern": "Dots Pattern",
                        "triangle_pattern": "Triangle Pattern",
                        "star_pattern": "Star Pattern",
                        "wave_pattern": "Wave Pattern",
                        "diamond_pattern": "Diamond Pattern",
                        "rhombus_pattern": "Rhombus Pattern",
                        "spiral_pattern": "Spiral Pattern",
                        "zigzag_pattern": "Zigzag Pattern",
                        "target_pattern": "Target Pattern",
                        "web_pattern": "Web Pattern",
                        "border_frame": "Border Frame",
                        "geometric_mix": "Geometric Mix",
                        "circus_pattern": "Circus Pattern",
                        "rainbow_arch": "Rainbow Arch",
                        "snowflake_pattern": "Snowflake Pattern",
                        "flower_pattern": "Flower Pattern",
                        "gear_pattern": "Gear Pattern",
                        "fire_pattern": "Fire Pattern"
                    }
                    grid_display_name = grid_name_map.get(grid_type, grid_type)

                    if status_callback:
                        status_callback(f"🎨 Applying {grid_display_name}...")

                    if progress_callback:
                        progress_callback(80)

                    # Create temp file for advanced grid processing
                    temp_output = self.generate_safe_filename(input_path, "temp_with_grid")

                    def grid_progress(p):
                        if progress_callback:
                            progress_callback(80 + int(p * 0.2))  # 80-100%

                    success, msg = self.apply_advanced_grid_post_processing(
                        output_path, temp_output, effects["grid_config"],
                        grid_progress, status_callback
                    )

                    if success:
                        # Replace original output with grid-processed version
                        try:
                            os.remove(output_path)
                            os.rename(temp_output, output_path)
                        except Exception as e:
                            return {"success": False, "error": f"Error replacing output: {e}"}
                    else:
                        # Cleanup temp file on error
                        if os.path.exists(temp_output):
                            try:
                                os.remove(temp_output)
                            except:
                                pass
                        return {"success": False, "error": f"Advanced grid error: {msg}"}

                if progress_callback:
                    progress_callback(100)
                if status_callback:
                    status_callback(f"✅ Hoàn thành xử lý: {os.path.basename(input_path)}")
                return {"success": True, "output_path": output_path}
            else:
                return {"success": False, "error": "Lỗi xử lý FFmpeg"}

        except Exception as e:
            if status_callback:
                status_callback(f"❌ Lỗi: {str(e)}")
            return {"success": False, "error": str(e)}





















    def has_audio_stream(self, video_path):
        """Check if video has audio stream"""
        try:
            # Hide CMD window completely
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            cmd = [
                self.ffmpeg_manager.ffmpeg_path,
                "-i", video_path,
                "-hide_banner", "-v", "quiet",
                "-show_streams", "-select_streams", "a",
                "-of", "csv=p=0"
            ]

            # Try ffprobe first
            ffprobe_path = self.ffmpeg_manager.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            if os.path.exists(ffprobe_path):
                cmd[0] = ffprobe_path

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10,
                                  startupinfo=startupinfo, creationflags=creationflags)
            return result.returncode == 0 and len(result.stdout.strip()) > 0
        except:
            return False

    def run_ffmpeg_command(self, cmd, progress_callback=None, status_callback=None):
        """Execute FFmpeg command with progress tracking"""
        try:
            import subprocess
            import platform
            import re

            # Debug: Print command
            if status_callback:
                status_callback(f"🔧 FFmpeg command: {' '.join(cmd)}")

            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            # Run FFmpeg command with proper encoding
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                encoding='utf-8',
                errors='replace',  # Handle encoding errors
                startupinfo=startupinfo,
                creationflags=creationflags
            )

            # Collect all output
            output_lines = []

            # Monitor progress
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    output_lines.append(output.strip())

                    if progress_callback:
                        # Try to extract progress from FFmpeg output
                        # Look for time=XX:XX:XX.XX pattern
                        time_match = re.search(r'time=(\d+):(\d+):(\d+\.\d+)', output)
                        if time_match:
                            hours = int(time_match.group(1))
                            minutes = int(time_match.group(2))
                            seconds = float(time_match.group(3))
                            current_time = hours * 3600 + minutes * 60 + seconds

                            # Estimate progress (rough approximation)
                            # For 3-second video, this gives reasonable progress
                            estimated_duration = 3.0  # seconds
                            progress = min(95, int((current_time / estimated_duration) * 100))
                            progress_callback(progress)

            # Wait for completion
            return_code = process.wait()

            if return_code == 0:
                if progress_callback:
                    progress_callback(100)
                if status_callback:
                    status_callback("✅ FFmpeg completed successfully")
                return True
            else:
                # Show last few lines of output for debugging
                error_output = '\n'.join(output_lines[-10:]) if output_lines else "No output"
                if status_callback:
                    status_callback(f"❌ FFmpeg error (code {return_code}):")
                    status_callback(f"   Last output: {error_output}")
                return False

        except Exception as e:
            if status_callback:
                status_callback(f"❌ FFmpeg execution error: {e}")
            import traceback
            traceback.print_exc()
            return False

    def apply_advanced_grid_post_processing(self, input_path, output_path, grid_config,
                                          progress_callback=None, status_callback=None):
        """Apply advanced grid effects using OpenCV post-processing WITH AUDIO"""
        try:
            import cv2

            grid_type = grid_config.get("type", "unknown")
            grid_name_map = {
                "random_grid": "Random Grid (Anti-Reup)",
                "dynamic_grid": "Dynamic Moving Grid",
                "asymmetric": "Asymmetric Grid",
                "fibonacci": "Fibonacci Spiral",
                "hexagon": "Hexagon Grid",
                "custom_pattern": "Custom Pattern",
                "multi_layer": "Multi-Layer Grid",
                # NEW COMPREHENSIVE PATTERNS
                "dots_pattern": "Dots Pattern",
                "triangle_pattern": "Triangle Pattern",
                "star_pattern": "Star Pattern",
                "wave_pattern": "Wave Pattern",
                "diamond_pattern": "Diamond Pattern",
                "rhombus_pattern": "Rhombus Pattern",
                "spiral_pattern": "Spiral Pattern",
                "zigzag_pattern": "Zigzag Pattern",
                "target_pattern": "Target Pattern",
                "web_pattern": "Web Pattern",
                "border_frame": "Border Frame",
                "geometric_mix": "Geometric Mix",
                "circus_pattern": "Circus Pattern",
                "rainbow_arch": "Rainbow Arch",
                "snowflake_pattern": "Snowflake Pattern",
                "flower_pattern": "Flower Pattern",
                "gear_pattern": "Gear Pattern",
                "fire_pattern": "Fire Pattern"
            }
            grid_display_name = grid_name_map.get(grid_type, grid_type)

            if status_callback:
                status_callback(f"🎨 Processing {grid_display_name}...")

            # Create temp video file (without audio)
            temp_video = self.generate_safe_filename(input_path, "temp_grid_video")

            # Open input video
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                return False, "Cannot open input video"

            # Get video properties
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Create output video writer (temp without audio)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(temp_video, fourcc, fps, (width, height))

            frame_count = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Apply advanced grid
                processed_frame = self.advanced_grid_processor.apply_advanced_grid(
                    frame, grid_config, frame_count, total_frames
                )

                out.write(processed_frame)
                frame_count += 1

                # Progress update
                if progress_callback and frame_count % 30 == 0:
                    progress = int((frame_count / total_frames) * 80)  # 0-80% for video processing
                    progress_callback(progress)

                if status_callback and frame_count % 60 == 0:
                    status_callback(f"🎨 {grid_display_name}: {frame_count}/{total_frames} frames")

            cap.release()
            out.release()

            # Now combine video with original audio using FFmpeg
            if status_callback:
                status_callback(f"🎵 Combining {grid_display_name} with audio...")

            if progress_callback:
                progress_callback(85)  # Video done, now audio

            import subprocess
            import platform

            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            # Check if original video has audio first
            check_cmd = [
                self.ffmpeg_manager.ffmpeg_path,
                "-i", input_path,
                "-hide_banner",
                "-f", "null", "-"
            ]

            check_result = subprocess.run(
                check_cmd, capture_output=True, text=True,
                startupinfo=startupinfo, creationflags=creationflags
            )

            has_audio = "Audio:" in check_result.stderr

            # Debug audio detection
            if status_callback:
                if has_audio:
                    status_callback(f"🎵 Audio detected in original video")
                else:
                    status_callback(f"🔇 No audio detected in original video")
                    status_callback(f"   FFmpeg stderr: {check_result.stderr[:200]}...")

            if has_audio:
                # Use FFmpeg to combine video + audio
                cmd = [
                    self.ffmpeg_manager.ffmpeg_path,
                    "-i", temp_video,  # Video input
                    "-i", input_path,  # Audio input (from original)
                    "-c:v", "copy",    # Copy video stream
                    "-c:a", "copy",    # Copy audio stream (faster)
                    "-map", "0:v:0",   # Map video from first input
                    "-map", "1:a:0",   # Map audio from second input
                    "-shortest",       # Match shortest stream
                    "-y",              # Overwrite output
                    output_path
                ]

                if status_callback:
                    status_callback(f"🎵 {grid_display_name} with audio preservation")
            else:
                # No audio in original, just copy processed video
                cmd = [
                    self.ffmpeg_manager.ffmpeg_path,
                    "-i", temp_video,  # Video input only
                    "-c:v", "copy",    # Copy video stream
                    "-y",              # Overwrite output
                    output_path
                ]

                if status_callback:
                    status_callback(f"📹 {grid_display_name} (no audio in original)")

            process = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, timeout=300, startupinfo=startupinfo, creationflags=creationflags
            )

            # Cleanup temp video
            if os.path.exists(temp_video):
                try:
                    os.remove(temp_video)
                except:
                    pass

            if process.returncode == 0:
                if status_callback:
                    status_callback(f"✅ {grid_display_name} applied successfully!")
                if progress_callback:
                    progress_callback(100)
                return True, f"{grid_display_name} applied successfully with audio"
            else:
                return False, f"Audio combination failed: {process.stderr}"

        except Exception as e:
            return False, f"Advanced grid error: {str(e)}"

from concurrent.futures import ThreadPoolExecutor, as_completed

def process_videos(video_paths, effects, progress_callback, on_complete, zoom_percent=110, status_callback=None, num_workers=2):
    """🚀 Optimized video processing with intelligent resource management"""
    processor = VideoProcessor()

    if not processor.ffmpeg_manager.is_available():
        if status_callback:
            status_callback("❌ FFmpeg không tìm thấy! Vui lòng cài đặt FFmpeg")
        if on_complete:
            on_complete()
        return

    # 🚀 RESPECT num_workers FROM UI LOGIC!
    total = len(video_paths)
    # DON'T OVERRIDE - use the passed num_workers value!

    if status_callback:
        status_callback(f"🚀 Bắt đầu xử lý {total} video(s) với {num_workers} workers")

    results = [None] * total
    success, fail = 0, 0

    def process_and_store(i, path):
        def local_progress(p):
            absolute = int((i / total) * 100 * 0.8) + int(p * 0.2)
            progress_callback(absolute)
        result = processor.process_single_video(path, effects, zoom_percent, status_callback, local_progress)
        results[i] = result
        return result

    # 🚀 INTELLIGENT PROCESSING LOGIC
    if num_workers == 1:
        # Sequential processing (GPU-optimized)
        if status_callback:
            status_callback("🚀 Sequential GPU Processing (Optimal for 1-3 videos)")

        for i, path in enumerate(video_paths):
            if status_callback:
                status_callback(f"📹 Đang xử lý: {os.path.basename(path)}")

            result = process_and_store(i, path)
            if result["success"]:
                success += 1
                if status_callback:
                    status_callback(f"✅ Hoàn thành: {os.path.basename(path)}")
            else:
                fail += 1
                if status_callback:
                    status_callback(f"❌ Lỗi: {os.path.basename(path)} - {result['error']}")
    else:
        # Multi-threading processing (4+ videos)
        if status_callback:
            status_callback(f"🔥 Multi-Threading Processing với {num_workers} workers")

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                # Submit all tasks
                futures = {executor.submit(process_and_store, i, path): i for i, path in enumerate(video_paths)}

                # Process results as they complete
                for future in concurrent.futures.as_completed(futures):
                    try:
                        result = future.result(timeout=300)  # 5 minute timeout per video
                        if result["success"]:
                            success += 1
                            if status_callback:
                                index = futures[future]
                                status_callback(f"✅ Hoàn thành: {os.path.basename(video_paths[index])}")
                        else:
                            fail += 1
                            if status_callback:
                                index = futures[future]
                                status_callback(f"❌ Lỗi: {os.path.basename(video_paths[index])} - {result['error']}")
                    except concurrent.futures.TimeoutError:
                        fail += 1
                        if status_callback:
                            index = futures[future]
                            status_callback(f"⏰ Timeout: {os.path.basename(video_paths[index])}")
                    except Exception as e:
                        fail += 1
                        if status_callback:
                            index = futures[future]
                            status_callback(f"💥 Exception: {os.path.basename(video_paths[index])} - {str(e)}")

    if progress_callback:
        progress_callback(100)

    # Determine processing mode for display
    processing_mode = "GPU Sequential" if num_workers == 1 else f"CPU Multi-Threading ({num_workers} workers)"

    if status_callback:
        status_callback(f"🎉 Hoàn thành: {success} thành công, {fail} lỗi | Tối ưu: {processing_mode}")

    if on_complete:
        on_complete()

