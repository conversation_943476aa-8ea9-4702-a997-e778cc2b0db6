#!/usr/bin/env python3
"""
Advanced Video Effects System
Hiệu ứng video nâng cao: motion effects, color grading, visual effects
"""

import cv2
import numpy as np
import os
import subprocess
from typing import Dict, List, Tuple

class AdvancedEffectsProcessor:
    def __init__(self):
        self.ffmpeg_path = None
        self.temp_dir = "temp_effects"
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def set_ffmpeg_path(self, path):
        """Thiết lập đường dẫn FFmpeg"""
        self.ffmpeg_path = path
    
    def apply_motion_effects(self, input_path, output_path, effect_type, settings, 
                           progress_callback=None, status_callback=None):
        """Áp dụng hiệu ứng chuyển động"""
        try:
            if status_callback:
                status_callback(f"🎬 Áp dụng {effect_type}...")
            
            if effect_type == "smooth_slowmo":
                return self._apply_smooth_slowmo(input_path, output_path, settings, progress_callback)
            elif effect_type == "speed_ramp":
                return self._apply_speed_ramp(input_path, output_path, settings, progress_callback)
            elif effect_type == "motion_blur":
                return self._apply_motion_blur(input_path, output_path, settings, progress_callback)
            elif effect_type == "stabilization":
                return self._apply_stabilization(input_path, output_path, settings, progress_callback)
            else:
                return False, f"Unknown motion effect: {effect_type}"
                
        except Exception as e:
            return False, f"Motion effect error: {str(e)}"
    
    def _apply_smooth_slowmo(self, input_path, output_path, settings, progress_callback):
        """Slow-motion mượt mà với frame interpolation"""
        slowmo_factor = settings.get('factor', 0.5)  # 0.5 = half speed
        interpolation_method = settings.get('method', 'minterpolate')
        
        if interpolation_method == 'minterpolate':
            # FFmpeg minterpolate filter for smooth slow motion
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-filter:v", f"minterpolate=fps=60:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1",
                "-filter:v", f"setpts={1/slowmo_factor}*PTS",
                "-c:v", "h264_nvenc", "-preset", "fast",
                "-y", output_path
            ]
        else:
            # Simple PTS adjustment
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-filter:v", f"setpts={1/slowmo_factor}*PTS",
                "-c:v", "h264_nvenc", "-preset", "fast",
                "-y", output_path
            ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_speed_ramp(self, input_path, output_path, settings, progress_callback):
        """Speed ramping - thay đổi tốc độ linh hoạt"""
        # Speed ramp points: [(time, speed), ...]
        ramp_points = settings.get('ramp_points', [(0, 1.0), (0.5, 0.5), (1.0, 1.0)])
        
        # Build complex filter for speed ramping
        filter_parts = []
        for i, (time_point, speed) in enumerate(ramp_points):
            if i == 0:
                continue
            prev_time, prev_speed = ramp_points[i-1]
            
            # Create segment with specific speed
            segment_filter = f"setpts={1/speed}*PTS"
            filter_parts.append(segment_filter)
        
        # For now, use simple speed change (complex ramping needs more advanced implementation)
        avg_speed = sum(speed for _, speed in ramp_points) / len(ramp_points)
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-filter:v", f"setpts={1/avg_speed}*PTS",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_motion_blur(self, input_path, output_path, settings, progress_callback):
        """Motion blur simulation"""
        blur_strength = settings.get('strength', 5)
        blur_angle = settings.get('angle', 0)  # degrees
        
        # Use FFmpeg's motion blur filter
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-filter:v", f"mblur=radius={blur_strength}:angle={blur_angle}",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_stabilization(self, input_path, output_path, settings, progress_callback):
        """Video stabilization"""
        smoothing = settings.get('smoothing', 10)
        
        # Two-pass stabilization
        # Pass 1: Analyze
        vectors_file = os.path.join(self.temp_dir, "vectors.trf")
        
        cmd1 = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", f"vidstabdetect=stepsize=6:shakiness=8:accuracy=9:result={vectors_file}",
            "-f", "null", "-"
        ]
        
        success1 = self._run_ffmpeg_command(cmd1, None)
        if not success1:
            return False, "Stabilization analysis failed"
        
        # Pass 2: Transform
        cmd2 = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", f"vidstabtransform=input={vectors_file}:zoom=1:smoothing={smoothing}",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd2, progress_callback)
    
    def apply_color_grading(self, input_path, output_path, grading_type, settings,
                          progress_callback=None, status_callback=None):
        """Áp dụng color grading"""
        try:
            if status_callback:
                status_callback(f"🌈 Áp dụng {grading_type}...")
            
            if grading_type == "cinematic":
                return self._apply_cinematic_grading(input_path, output_path, settings, progress_callback)
            elif grading_type == "vintage":
                return self._apply_vintage_grading(input_path, output_path, settings, progress_callback)
            elif grading_type == "hdr_tone_mapping":
                return self._apply_hdr_tone_mapping(input_path, output_path, settings, progress_callback)
            elif grading_type == "custom_lut":
                return self._apply_custom_lut(input_path, output_path, settings, progress_callback)
            else:
                return False, f"Unknown color grading: {grading_type}"
                
        except Exception as e:
            return False, f"Color grading error: {str(e)}"
    
    def _apply_cinematic_grading(self, input_path, output_path, settings, progress_callback):
        """Cinematic color grading"""
        # Cinematic look: teal and orange, desaturated, high contrast
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", """
            eq=contrast=1.2:brightness=0.05:saturation=0.8,
            curves=all='0/0 0.25/0.15 0.5/0.5 0.75/0.85 1/1':
            red='0/0 0.25/0.2 0.5/0.5 0.75/0.8 1/1':
            blue='0/0 0.25/0.1 0.5/0.5 0.75/0.9 1/1',
            colorbalance=rs=0.1:gs=-0.05:bs=-0.1:rm=0.05:gm=0:bm=-0.05
            """.replace('\n', '').replace(' ', ''),
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_vintage_grading(self, input_path, output_path, settings, progress_callback):
        """Vintage film look"""
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", """
            eq=contrast=1.1:brightness=0.1:saturation=0.7:gamma=1.2,
            curves=all='0/0.1 0.25/0.2 0.5/0.5 0.75/0.8 1/0.9',
            noise=alls=10:allf=t,
            vignette=angle=PI/4:mode=backward
            """.replace('\n', '').replace(' ', ''),
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def apply_visual_effects(self, input_path, output_path, effect_type, settings,
                           progress_callback=None, status_callback=None):
        """Áp dụng hiệu ứng hình ảnh"""
        try:
            if status_callback:
                status_callback(f"✨ Áp dụng {effect_type}...")
            
            if effect_type == "particle_overlay":
                return self._apply_particle_overlay(input_path, output_path, settings, progress_callback)
            elif effect_type == "light_leaks":
                return self._apply_light_leaks(input_path, output_path, settings, progress_callback)
            elif effect_type == "film_grain":
                return self._apply_film_grain(input_path, output_path, settings, progress_callback)
            elif effect_type == "glitch":
                return self._apply_glitch_effect(input_path, output_path, settings, progress_callback)
            elif effect_type == "lens_flare":
                return self._apply_lens_flare(input_path, output_path, settings, progress_callback)
            else:
                return False, f"Unknown visual effect: {effect_type}"
                
        except Exception as e:
            return False, f"Visual effect error: {str(e)}"
    
    def _apply_particle_overlay(self, input_path, output_path, settings, progress_callback):
        """Particle effects overlay"""
        particle_type = settings.get('type', 'snow')
        density = settings.get('density', 0.5)
        
        if particle_type == 'snow':
            # Create snow effect using noise and motion
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-vf", f"noise=alls=20:allf=t+u,geq=lum='if(gt(lum(X,Y),200),255,0)':cb=128:cr=128",
                "-c:v", "h264_nvenc", "-preset", "fast",
                "-y", output_path
            ]
        elif particle_type == 'dust':
            cmd = [
                self.ffmpeg_path, "-i", input_path,
                "-vf", f"noise=alls=15:allf=t,geq=lum='if(gt(lum(X,Y),220),255,lum(X,Y))':cb=128:cr=128",
                "-c:v", "h264_nvenc", "-preset", "fast",
                "-y", output_path
            ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_light_leaks(self, input_path, output_path, settings, progress_callback):
        """Light leaks effect"""
        intensity = settings.get('intensity', 0.3)
        color = settings.get('color', 'orange')
        
        # Create light leak overlay
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", f"curves=all='0/0 0.3/{intensity} 0.7/{intensity} 1/1',colorbalance=rs=0.2:gs=0.1:bs=-0.1",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_film_grain(self, input_path, output_path, settings, progress_callback):
        """Film grain effect"""
        strength = settings.get('strength', 20)
        
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", f"noise=alls={strength}:allf=t+u",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _apply_glitch_effect(self, input_path, output_path, settings, progress_callback):
        """Glitch effect for trendy videos"""
        intensity = settings.get('intensity', 0.1)
        
        # Digital glitch effect
        cmd = [
            self.ffmpeg_path, "-i", input_path,
            "-vf", f"noise=alls=30:allf=t,datascope=mode=mono:axis=1,geq=r='if(mod(X+Y,20),r(X,Y),255)':g='g(X,Y)':b='b(X,Y)'",
            "-c:v", "h264_nvenc", "-preset", "fast",
            "-y", output_path
        ]
        
        return self._run_ffmpeg_command(cmd, progress_callback)
    
    def _run_ffmpeg_command(self, cmd, progress_callback):
        """Chạy lệnh FFmpeg với progress tracking"""
        try:
            import platform
            
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                creationflags = 0x08000000 | 0x00000008
            
            process = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, timeout=600, startupinfo=startupinfo, creationflags=creationflags
            )
            
            if progress_callback:
                progress_callback(100)
            
            return process.returncode == 0, process.stderr if process.returncode != 0 else "Success"
            
        except Exception as e:
            return False, str(e)

# Preset configurations
MOTION_PRESETS = {
    "smooth_slowmo_2x": {
        "type": "smooth_slowmo",
        "settings": {"factor": 0.5, "method": "minterpolate"}
    },
    "dramatic_slowmo": {
        "type": "smooth_slowmo", 
        "settings": {"factor": 0.25, "method": "minterpolate"}
    },
    "speed_boost": {
        "type": "speed_ramp",
        "settings": {"ramp_points": [(0, 1.0), (0.5, 2.0), (1.0, 1.0)]}
    }
}

COLOR_PRESETS = {
    "hollywood_cinematic": {
        "type": "cinematic",
        "settings": {"style": "teal_orange"}
    },
    "vintage_film": {
        "type": "vintage", 
        "settings": {"era": "70s"}
    },
    "instagram_warm": {
        "type": "cinematic",
        "settings": {"style": "warm_highlights"}
    }
}

VISUAL_PRESETS = {
    "winter_particles": {
        "type": "particle_overlay",
        "settings": {"type": "snow", "density": 0.3}
    },
    "film_aesthetic": {
        "type": "film_grain",
        "settings": {"strength": 25}
    },
    "trendy_glitch": {
        "type": "glitch",
        "settings": {"intensity": 0.15}
    }
}

# Test function
if __name__ == "__main__":
    processor = AdvancedEffectsProcessor()
    print("🎬 Advanced Effects System initialized")
    print("Available presets:")
    print("Motion:", list(MOTION_PRESETS.keys()))
    print("Color:", list(COLOR_PRESETS.keys()))
    print("Visual:", list(VISUAL_PRESETS.keys()))
