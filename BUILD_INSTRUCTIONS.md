# 🚀 BUILD EXE INSTRUCTIONS

## 📋 Prerequisites
1. Python 3.8+ installed
2. All dependencies installed: `pip install -r requirements.txt`
3. PyInstaller: `pip install pyinstaller`

## 🔨 Build Commands

### Simple Build:
```bash
pyinstaller --onefile --windowed launch_complete.py
```

### Advanced Build (Recommended):
```bash
pyinstaller --onefile --windowed \
    --name "Enhanced_TikTok_Bypass_Tool" \
    --add-data "ffmpeg;ffmpeg" \
    --add-data "models;models" \
    --hidden-import=cv2 \
    --hidden-import=torch \
    --hidden-import=PyQt5 \
    launch_complete.py
```

## 📁 Output
- EXE file will be in `dist/` folder
- Size: ~200-500 MB (includes all dependencies)
- Portable: Can run on any Windows PC

## ⚠️ Notes
- First run may be slow (PyTorch initialization)
- Antivirus may flag as false positive
- Include ffmpeg and models folders with EXE
