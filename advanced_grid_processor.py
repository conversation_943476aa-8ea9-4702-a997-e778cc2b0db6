#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Grid Processor - Anti-Reup Detection
Bypass TikTok reup detection with dynamic grids
"""

import cv2
import numpy as np
import math
import random
import time

class AdvancedGridProcessor:
    def __init__(self):
        self.frame_count = 0
        self.total_frames = 0
        self.start_time = time.time()
        self.gpu_available = self._check_gpu_support()

    def _check_gpu_support(self):
        """Check if OpenCV GPU support is available"""
        try:
            # Check if CUDA is available in OpenCV
            return cv2.cuda.getCudaEnabledDeviceCount() > 0
        except:
            return False
    
    def apply_advanced_grid(self, frame, grid_config, frame_number=0, total_frames=1):
        """Apply advanced grid with anti-reup features"""
        if not grid_config:
            return frame
        
        self.frame_count = frame_number
        self.total_frames = total_frames
        
        # Get frame dimensions
        height, width = frame.shape[:2]
        
        # Create overlay
        overlay = frame.copy()
        
        # Apply grid based on type
        grid_type = grid_config.get("type", "3x3")
        
        if grid_type == "random_grid":
            overlay = self._apply_random_grid(overlay, grid_config)
        elif grid_type == "dynamic_grid":
            overlay = self._apply_dynamic_grid(overlay, grid_config)
        elif grid_type == "asymmetric":
            overlay = self._apply_asymmetric_grid(overlay, grid_config)
        elif grid_type == "fibonacci":
            overlay = self._apply_fibonacci_spiral(overlay, grid_config)
        elif grid_type == "hexagon":
            overlay = self._apply_hexagon_grid(overlay, grid_config)
        elif grid_type == "custom_pattern":
            overlay = self._apply_custom_pattern(overlay, grid_config)
        elif grid_type == "multi_layer":
            overlay = self._apply_multi_layer_grid(overlay, grid_config)
        # NEW COMPREHENSIVE PATTERNS
        elif grid_type == "dots_pattern":
            overlay = self._apply_dots_pattern(overlay, grid_config)
        elif grid_type == "triangle_pattern":
            overlay = self._apply_triangle_pattern(overlay, grid_config)
        elif grid_type == "star_pattern":
            overlay = self._apply_star_pattern(overlay, grid_config)
        elif grid_type == "wave_pattern":
            overlay = self._apply_wave_pattern(overlay, grid_config)
        elif grid_type == "diamond_pattern":
            overlay = self._apply_diamond_pattern(overlay, grid_config)
        elif grid_type == "rhombus_pattern":
            overlay = self._apply_rhombus_pattern(overlay, grid_config)
        elif grid_type == "spiral_pattern":
            overlay = self._apply_spiral_pattern(overlay, grid_config)
        elif grid_type == "zigzag_pattern":
            overlay = self._apply_zigzag_pattern(overlay, grid_config)
        elif grid_type == "target_pattern":
            overlay = self._apply_target_pattern(overlay, grid_config)
        elif grid_type == "web_pattern":
            overlay = self._apply_web_pattern(overlay, grid_config)
        elif grid_type == "border_frame":
            overlay = self._apply_border_frame(overlay, grid_config)
        elif grid_type == "geometric_mix":
            overlay = self._apply_geometric_mix(overlay, grid_config)
        elif grid_type == "circus_pattern":
            overlay = self._apply_circus_pattern(overlay, grid_config)
        elif grid_type == "rainbow_arch":
            overlay = self._apply_rainbow_arch(overlay, grid_config)
        elif grid_type == "snowflake_pattern":
            overlay = self._apply_snowflake_pattern(overlay, grid_config)
        elif grid_type == "flower_pattern":
            overlay = self._apply_flower_pattern(overlay, grid_config)
        elif grid_type == "gear_pattern":
            overlay = self._apply_gear_pattern(overlay, grid_config)
        elif grid_type == "fire_pattern":
            overlay = self._apply_fire_pattern(overlay, grid_config)
        else:
            # Standard grids with anti-reup enhancements
            overlay = self._apply_standard_grid_enhanced(overlay, grid_config)
        
        # Apply timing effects
        overlay = self._apply_timing_effects(frame, overlay, grid_config)
        
        return overlay
    
    def _apply_random_grid(self, frame, config):
        """Random grid positions to bypass detection"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Random grid with varying positions
        intensity = config.get("anti_reup_intensity", 5)
        randomness = intensity * 10  # 10-100 pixel variation
        
        # Vertical lines
        for i in range(1, 4):  # 3x3 base
            x = int(width * i / 4) + random.randint(-randomness, randomness)
            x = max(0, min(width-1, x))
            cv2.line(frame, (x, 0), (x, height), color, thickness)
        
        # Horizontal lines
        for i in range(1, 4):
            y = int(height * i / 4) + random.randint(-randomness, randomness)
            y = max(0, min(height-1, y))
            cv2.line(frame, (0, y), (width, y), color, thickness)
        
        return frame
    
    def _apply_dynamic_grid(self, frame, config):
        """Moving grid lines"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Calculate movement based on frame number
        time_factor = self.frame_count / max(1, self.total_frames)
        movement = int(20 * math.sin(time_factor * 4 * math.pi))
        
        # Moving vertical lines
        for i in range(1, 4):
            x = int(width * i / 4) + movement
            if 0 <= x < width:
                cv2.line(frame, (x, 0), (x, height), color, thickness)
        
        # Moving horizontal lines
        for i in range(1, 4):
            y = int(height * i / 4) + movement
            if 0 <= y < height:
                cv2.line(frame, (0, y), (width, y), color, thickness)
        
        return frame
    
    def _apply_asymmetric_grid(self, frame, config):
        """Asymmetric grid pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Asymmetric divisions
        v_lines = [width//3, width*2//3 + 50, width*4//5]
        h_lines = [height//4, height//2 + 30, height*3//4]
        
        for x in v_lines:
            if 0 <= x < width:
                cv2.line(frame, (x, 0), (x, height), color, thickness)
        
        for y in h_lines:
            if 0 <= y < height:
                cv2.line(frame, (0, y), (width, y), color, thickness)
        
        return frame
    
    def _apply_fibonacci_spiral(self, frame, config):
        """Fibonacci spiral overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Fibonacci spiral
        center_x, center_y = width//2, height//2
        max_radius = min(width, height) // 3
        
        points = []
        for i in range(100):
            angle = i * 0.2
            radius = (i / 100) * max_radius
            x = int(center_x + radius * math.cos(angle))
            y = int(center_y + radius * math.sin(angle))
            if 0 <= x < width and 0 <= y < height:
                points.append((x, y))
        
        # Draw spiral
        for i in range(1, len(points)):
            cv2.line(frame, points[i-1], points[i], color, thickness)
        
        return frame
    
    def _apply_hexagon_grid(self, frame, config):
        """Hexagonal grid pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Hexagon parameters
        hex_size = min(width, height) // 8
        
        for row in range(-2, height//hex_size + 2):
            for col in range(-2, width//hex_size + 2):
                # Hexagon center
                x = col * hex_size * 1.5
                y = row * hex_size * math.sqrt(3)
                if row % 2:
                    x += hex_size * 0.75
                
                # Draw hexagon
                points = []
                for i in range(6):
                    angle = i * math.pi / 3
                    px = int(x + hex_size * math.cos(angle))
                    py = int(y + hex_size * math.sin(angle))
                    points.append((px, py))
                
                # Draw hexagon lines
                for i in range(6):
                    p1 = points[i]
                    p2 = points[(i+1) % 6]
                    if (0 <= p1[0] < width and 0 <= p1[1] < height and
                        0 <= p2[0] < width and 0 <= p2[1] < height):
                        cv2.line(frame, p1, p2, color, thickness)
        
        return frame
    
    def _apply_custom_pattern(self, frame, config):
        """Custom geometric pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        
        # Diamond pattern
        center_x, center_y = width//2, height//2
        
        # Multiple diamonds
        for size in [50, 100, 150]:
            points = [
                (center_x, center_y - size),
                (center_x + size, center_y),
                (center_x, center_y + size),
                (center_x - size, center_y)
            ]
            
            for i in range(4):
                p1 = points[i]
                p2 = points[(i+1) % 4]
                if (0 <= p1[0] < width and 0 <= p1[1] < height and
                    0 <= p2[0] < width and 0 <= p2[1] < height):
                    cv2.line(frame, p1, p2, color, thickness)
        
        return frame
    
    def _apply_multi_layer_grid(self, frame, config):
        """Multiple overlapping grids"""
        height, width = frame.shape[:2]
        thickness = config.get("line_width", 1)
        
        # Layer 1: 3x3 grid
        color1 = self._get_color_by_name("white")
        for i in range(1, 3):
            x = width * i // 3
            y = height * i // 3
            cv2.line(frame, (x, 0), (x, height), color1, thickness)
            cv2.line(frame, (0, y), (width, y), color1, thickness)
        
        # Layer 2: 5x5 grid (lighter)
        color2 = self._get_color_by_name("blue")
        for i in range(1, 5):
            x = width * i // 5
            y = height * i // 5
            cv2.line(frame, (x, 0), (x, height), color2, thickness)
            cv2.line(frame, (0, y), (width, y), color2, thickness)
        
        return frame
    
    def _apply_standard_grid_enhanced(self, frame, config):
        """Enhanced standard grids with anti-reup features"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)
        grid_type = config.get("type", "3x3")
        
        # Add randomness if enabled
        offset_x = offset_y = 0
        if config.get("random_position"):
            intensity = config.get("anti_reup_intensity", 5)
            offset_x = random.randint(-intensity*2, intensity*2)
            offset_y = random.randint(-intensity*2, intensity*2)
        
        # Apply animation offset
        if config.get("animated"):
            time_factor = self.frame_count / max(1, self.total_frames)
            offset_x += int(10 * math.sin(time_factor * 2 * math.pi))
            offset_y += int(10 * math.cos(time_factor * 2 * math.pi))
        
        # Draw grid based on type
        if grid_type == "3x3":
            divisions = 3
        elif grid_type == "4x4":
            divisions = 4
        elif grid_type == "5x5":
            divisions = 5
        elif grid_type == "6x6":
            divisions = 6
        else:
            divisions = 3
        
        # Draw grid lines
        for i in range(1, divisions):
            x = width * i // divisions + offset_x
            y = height * i // divisions + offset_y
            
            if 0 <= x < width:
                cv2.line(frame, (x, 0), (x, height), color, thickness)
            if 0 <= y < height:
                cv2.line(frame, (0, y), (width, y), color, thickness)
        
        return frame
    
    def _apply_timing_effects(self, original, overlay, config):
        """Apply timing-based effects"""
        timing = config.get("timing", "luôn_bật")

        # Map Vietnamese timing to English
        timing_map = {
            "luôn_bật": "always",
            "3_giây_đầu": "first_3s",
            "3_giây_cuối": "last_3s",
            "mờ_dần": "fade_inout",
            "nhấp_nháy": "pulse"
        }

        timing_en = timing_map.get(timing, "always")

        if timing_en == "always" or timing == "always":
            opacity = self._get_dynamic_opacity(config)
            return cv2.addWeighted(original, 1-opacity, overlay, opacity, 0)
        
        # Calculate time factor
        time_factor = self.frame_count / max(1, self.total_frames)

        if timing_en == "first_3s":
            # Assuming 30fps, first 3s = first 90 frames
            if self.frame_count < 90:
                opacity = self._get_dynamic_opacity(config)
                return cv2.addWeighted(original, 1-opacity, overlay, opacity, 0)

        elif timing_en == "last_3s":
            if self.frame_count > self.total_frames - 90:
                opacity = self._get_dynamic_opacity(config)
                return cv2.addWeighted(original, 1-opacity, overlay, opacity, 0)

        elif timing_en == "fade_inout":
            if time_factor < 0.2:  # Fade in first 20%
                opacity = self._get_dynamic_opacity(config) * (time_factor / 0.2)
            elif time_factor > 0.8:  # Fade out last 20%
                opacity = self._get_dynamic_opacity(config) * ((1 - time_factor) / 0.2)
            else:
                opacity = self._get_dynamic_opacity(config)
            return cv2.addWeighted(original, 1-opacity, overlay, opacity, 0)

        elif timing_en == "pulse":
            pulse = (math.sin(time_factor * 8 * math.pi) + 1) / 2  # 0-1
            opacity = self._get_dynamic_opacity(config) * pulse
            return cv2.addWeighted(original, 1-opacity, overlay, opacity, 0)

        return original
    
    def _get_dynamic_opacity(self, config):
        """Get dynamic opacity"""
        base_opacity = config.get("opacity", 0.3)
        
        if config.get("dynamic_opacity"):
            time_factor = self.frame_count / max(1, self.total_frames)
            breathing = (math.sin(time_factor * 4 * math.pi) + 1) / 2  # 0-1
            return base_opacity * (0.5 + 0.5 * breathing)
        
        return base_opacity
    
    def _get_dynamic_color(self, config):
        """Get dynamic color based on config"""
        color_name = config.get("color", "white")
        
        if color_name == "random_color":
            return (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        elif color_name == "rainbow":
            time_factor = self.frame_count / max(1, self.total_frames)
            hue = int(time_factor * 360) % 360
            # Convert HSV to BGR
            hsv = np.uint8([[[hue, 255, 255]]])
            bgr = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
            return tuple(map(int, bgr[0][0]))
        elif color_name == "flashing":
            if self.frame_count % 10 < 5:
                return self._get_color_by_name("white")
            else:
                return self._get_color_by_name("red")
        else:
            return self._get_color_by_name(color_name)
    
    def _get_color_by_name(self, color_name):
        """Get BGR color by name"""
        colors = {
            "white": (255, 255, 255),
            "red": (0, 0, 255),
            "blue": (255, 0, 0),
            "green": (0, 255, 0),
            "yellow": (0, 255, 255),
            "purple": (255, 0, 255),
            "orange": (0, 165, 255),
            "pink": (203, 192, 255)
        }
        return colors.get(color_name, (255, 255, 255))

    # NEW COMPREHENSIVE PATTERN IMPLEMENTATIONS
    def _apply_dots_pattern(self, frame, config):
        """Dots pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        radius = config.get("line_width", 2) * 2

        # Create dot grid
        spacing_x = width // 8
        spacing_y = height // 8

        for y in range(spacing_y//2, height, spacing_y):
            for x in range(spacing_x//2, width, spacing_x):
                # Add randomness for anti-reup
                intensity = config.get("anti_reup_intensity", 5)
                offset_x = random.randint(-intensity*2, intensity*2)
                offset_y = random.randint(-intensity*2, intensity*2)

                dot_x = max(radius, min(width-radius, x + offset_x))
                dot_y = max(radius, min(height-radius, y + offset_y))

                cv2.circle(frame, (dot_x, dot_y), radius, color, -1)

        return frame

    def _apply_triangle_pattern(self, frame, config):
        """Triangle pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create triangle grid
        size = min(width, height) // 10
        spacing_x = width // 6
        spacing_y = height // 6

        for y in range(spacing_y//2, height, spacing_y):
            for x in range(spacing_x//2, width, spacing_x):
                # Triangle points
                pt1 = (x, y - size//2)
                pt2 = (x - size//2, y + size//2)
                pt3 = (x + size//2, y + size//2)

                # Add randomness
                intensity = config.get("anti_reup_intensity", 5)
                offset = random.randint(-intensity, intensity)
                pt1 = (pt1[0] + offset, pt1[1])
                pt2 = (pt2[0] + offset, pt2[1])
                pt3 = (pt3[0] + offset, pt3[1])

                # Draw triangle
                cv2.line(frame, pt1, pt2, color, thickness)
                cv2.line(frame, pt2, pt3, color, thickness)
                cv2.line(frame, pt3, pt1, color, thickness)

        return frame

    def _apply_star_pattern(self, frame, config):
        """Star pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create star grid
        spacing_x = width // 5
        spacing_y = height // 5
        size = min(spacing_x, spacing_y) // 3

        for y in range(spacing_y//2, height, spacing_y):
            for x in range(spacing_x//2, width, spacing_x):
                # 5-pointed star
                points = []
                for i in range(10):
                    angle = i * math.pi / 5
                    radius = size if i % 2 == 0 else size // 2
                    px = int(x + radius * math.cos(angle - math.pi/2))
                    py = int(y + radius * math.sin(angle - math.pi/2))
                    points.append((px, py))

                # Draw star
                for i in range(len(points)):
                    next_i = (i + 1) % len(points)
                    cv2.line(frame, points[i], points[next_i], color, thickness)

        return frame

    def _apply_wave_pattern(self, frame, config):
        """Wave pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Horizontal waves
        wave_count = 5
        for i in range(wave_count):
            y_base = height * (i + 1) // (wave_count + 1)
            amplitude = height // 20
            frequency = 4 * math.pi / width

            points = []
            for x in range(0, width, 5):
                y = int(y_base + amplitude * math.sin(frequency * x + self.frame_count * 0.1))
                points.append((x, y))

            # Draw wave
            for i in range(len(points) - 1):
                cv2.line(frame, points[i], points[i+1], color, thickness)

        return frame

    def _apply_diamond_pattern(self, frame, config):
        """Diamond pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create diamond grid
        spacing_x = width // 6
        spacing_y = height // 6
        size = min(spacing_x, spacing_y) // 3

        for y in range(spacing_y//2, height, spacing_y):
            for x in range(spacing_x//2, width, spacing_x):
                # Diamond points
                top = (x, y - size)
                right = (x + size, y)
                bottom = (x, y + size)
                left = (x - size, y)

                # Draw diamond
                cv2.line(frame, top, right, color, thickness)
                cv2.line(frame, right, bottom, color, thickness)
                cv2.line(frame, bottom, left, color, thickness)
                cv2.line(frame, left, top, color, thickness)

        return frame

    def _apply_rhombus_pattern(self, frame, config):
        """Rhombus pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create rhombus grid (rotated squares)
        spacing_x = width // 5
        spacing_y = height // 5
        size = min(spacing_x, spacing_y) // 4

        for y in range(spacing_y//2, height, spacing_y):
            for x in range(spacing_x//2, width, spacing_x):
                # Rhombus points (45-degree rotated square)
                points = [
                    (x, y - size),
                    (x + size, y),
                    (x, y + size),
                    (x - size, y)
                ]

                # Draw rhombus
                for i in range(len(points)):
                    next_i = (i + 1) % len(points)
                    cv2.line(frame, points[i], points[next_i], color, thickness)

        return frame

    def _apply_spiral_pattern(self, frame, config):
        """Spiral pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Multiple spirals
        centers = [(width//4, height//4), (3*width//4, height//4),
                  (width//4, 3*height//4), (3*width//4, 3*height//4)]

        for center_x, center_y in centers:
            points = []
            max_radius = min(width, height) // 8

            for i in range(50):
                angle = i * 0.3
                radius = (i / 50) * max_radius
                x = int(center_x + radius * math.cos(angle))
                y = int(center_y + radius * math.sin(angle))
                if 0 <= x < width and 0 <= y < height:
                    points.append((x, y))

            # Draw spiral
            for i in range(1, len(points)):
                cv2.line(frame, points[i-1], points[i], color, thickness)

        return frame

    def _apply_zigzag_pattern(self, frame, config):
        """Zigzag pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Horizontal zigzags
        zigzag_count = 6
        amplitude = width // 20

        for i in range(zigzag_count):
            y = height * (i + 1) // (zigzag_count + 1)
            points = []

            for x in range(0, width, amplitude):
                if (x // amplitude) % 2 == 0:
                    points.append((x, y - amplitude//2))
                else:
                    points.append((x, y + amplitude//2))

            # Draw zigzag
            for i in range(len(points) - 1):
                cv2.line(frame, points[i], points[i+1], color, thickness)

        return frame

    def _apply_target_pattern(self, frame, config):
        """Target pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Concentric circles (target)
        center_x, center_y = width // 2, height // 2
        max_radius = min(width, height) // 4

        for i in range(1, 6):
            radius = (max_radius * i) // 5
            cv2.circle(frame, (center_x, center_y), radius, color, thickness)

        # Cross lines
        cv2.line(frame, (center_x - max_radius, center_y),
                (center_x + max_radius, center_y), color, thickness)
        cv2.line(frame, (center_x, center_y - max_radius),
                (center_x, center_y + max_radius), color, thickness)

        return frame

    def _apply_web_pattern(self, frame, config):
        """Web pattern overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        center_x, center_y = width // 2, height // 2

        # Radial lines
        for i in range(8):
            angle = i * math.pi / 4
            end_x = int(center_x + (width//2) * math.cos(angle))
            end_y = int(center_y + (height//2) * math.sin(angle))
            cv2.line(frame, (center_x, center_y), (end_x, end_y), color, thickness)

        # Concentric polygons
        for radius in range(50, min(width, height)//2, 50):
            points = []
            for i in range(8):
                angle = i * math.pi / 4
                x = int(center_x + radius * math.cos(angle))
                y = int(center_y + radius * math.sin(angle))
                points.append((x, y))

            for i in range(len(points)):
                next_i = (i + 1) % len(points)
                cv2.line(frame, points[i], points[next_i], color, thickness)

        return frame

    def _apply_border_frame(self, frame, config):
        """Border frame overlay"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2) * 3

        # Multiple border frames
        for i in range(3):
            margin = 20 + i * 15
            cv2.rectangle(frame, (margin, margin),
                         (width - margin, height - margin), color, thickness)

        # Corner decorations
        corner_size = 30
        corners = [(20, 20), (width-50, 20), (20, height-50), (width-50, height-50)]

        for x, y in corners:
            # Draw corner decoration
            cv2.line(frame, (x, y), (x + corner_size, y), color, thickness)
            cv2.line(frame, (x, y), (x, y + corner_size), color, thickness)

        return frame

    def _apply_geometric_mix(self, frame, config):
        """Geometric mix pattern - combination of shapes"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Mix of circles, squares, and triangles
        spacing = min(width, height) // 8

        for y in range(spacing, height, spacing):
            for x in range(spacing, width, spacing):
                shape_type = (x + y) % 3

                if shape_type == 0:  # Circle
                    cv2.circle(frame, (x, y), spacing//4, color, thickness)
                elif shape_type == 1:  # Square
                    half_size = spacing//4
                    cv2.rectangle(frame, (x-half_size, y-half_size),
                                (x+half_size, y+half_size), color, thickness)
                else:  # Triangle
                    pts = np.array([[x, y-spacing//4], [x-spacing//4, y+spacing//4],
                                   [x+spacing//4, y+spacing//4]], np.int32)
                    cv2.polylines(frame, [pts], True, color, thickness)

        return frame

    def _apply_circus_pattern(self, frame, config):
        """Circus pattern - colorful stripes and stars"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Diagonal stripes
        stripe_spacing = 30
        for i in range(-height, width, stripe_spacing):
            cv2.line(frame, (i, 0), (i + height, height), color, thickness)

        # Add stars at intersections
        star_spacing = 100
        for y in range(star_spacing, height, star_spacing):
            for x in range(star_spacing, width, star_spacing):
                # Small 4-pointed star
                star_size = 15
                cv2.line(frame, (x-star_size, y), (x+star_size, y), color, thickness)
                cv2.line(frame, (x, y-star_size), (x, y+star_size), color, thickness)

        return frame

    def _apply_rainbow_arch(self, frame, config):
        """Rainbow arch pattern"""
        height, width = frame.shape[:2]
        thickness = config.get("line_width", 2) * 2

        center_x, center_y = width // 2, height
        colors = [(255, 0, 0), (255, 127, 0), (255, 255, 0), (0, 255, 0),
                 (0, 0, 255), (75, 0, 130), (148, 0, 211)]  # Rainbow colors

        # Draw rainbow arches
        for i, color in enumerate(colors):
            radius = (height // 2) - (i * 20)
            if radius > 0:
                cv2.ellipse(frame, (center_x, center_y), (radius, radius),
                           0, 0, 180, color, thickness)

        return frame

    def _apply_snowflake_pattern(self, frame, config):
        """Snowflake pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create snowflakes at regular intervals
        spacing_x = width // 6
        spacing_y = height // 6

        for y in range(spacing_y, height, spacing_y):
            for x in range(spacing_x, width, spacing_x):
                # 6-pointed snowflake
                size = spacing_x // 4

                # Main lines
                cv2.line(frame, (x-size, y), (x+size, y), color, thickness)
                cv2.line(frame, (x, y-size), (x, y+size), color, thickness)
                cv2.line(frame, (x-size//2, y-size//2), (x+size//2, y+size//2), color, thickness)
                cv2.line(frame, (x-size//2, y+size//2), (x+size//2, y-size//2), color, thickness)

                # Small branches
                branch_size = size // 3
                for angle in [0, 60, 120]:
                    rad = math.radians(angle)
                    end_x = int(x + size * math.cos(rad))
                    end_y = int(y + size * math.sin(rad))

                    # Small branches at the end
                    cv2.line(frame, (end_x, end_y),
                            (int(end_x + branch_size * math.cos(rad + 0.5)),
                             int(end_y + branch_size * math.sin(rad + 0.5))), color, thickness)
                    cv2.line(frame, (end_x, end_y),
                            (int(end_x + branch_size * math.cos(rad - 0.5)),
                             int(end_y + branch_size * math.sin(rad - 0.5))), color, thickness)

        return frame

    def _apply_flower_pattern(self, frame, config):
        """Flower pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create flowers
        spacing_x = width // 5
        spacing_y = height // 5

        for y in range(spacing_y, height, spacing_y):
            for x in range(spacing_x, width, spacing_x):
                # 5-petal flower
                petal_size = spacing_x // 4

                for i in range(5):
                    angle = i * 2 * math.pi / 5
                    petal_x = int(x + petal_size * math.cos(angle))
                    petal_y = int(y + petal_size * math.sin(angle))

                    # Draw petal as ellipse
                    cv2.ellipse(frame, (petal_x, petal_y), (petal_size//2, petal_size//3),
                               int(math.degrees(angle)), 0, 360, color, thickness)

                # Center circle
                cv2.circle(frame, (x, y), petal_size//4, color, thickness)

        return frame

    def _apply_gear_pattern(self, frame, config):
        """Gear pattern"""
        height, width = frame.shape[:2]
        color = self._get_dynamic_color(config)
        thickness = config.get("line_width", 2)

        # Create gears
        spacing_x = width // 4
        spacing_y = height // 4

        for y in range(spacing_y, height, spacing_y):
            for x in range(spacing_x, width, spacing_x):
                # Gear with teeth
                gear_radius = spacing_x // 4
                teeth_count = 8

                # Main circle
                cv2.circle(frame, (x, y), gear_radius, color, thickness)

                # Teeth
                for i in range(teeth_count):
                    angle = i * 2 * math.pi / teeth_count
                    inner_x = int(x + gear_radius * math.cos(angle))
                    inner_y = int(y + gear_radius * math.sin(angle))
                    outer_x = int(x + (gear_radius + 10) * math.cos(angle))
                    outer_y = int(y + (gear_radius + 10) * math.sin(angle))

                    cv2.line(frame, (inner_x, inner_y), (outer_x, outer_y), color, thickness)

                # Center hole
                cv2.circle(frame, (x, y), gear_radius//3, color, thickness)

        return frame

    def _apply_fire_pattern(self, frame, config):
        """Fire pattern"""
        height, width = frame.shape[:2]
        thickness = config.get("line_width", 2)

        # Fire colors (red to yellow gradient)
        fire_colors = [(0, 0, 255), (0, 127, 255), (0, 255, 255), (0, 255, 127)]

        # Create flame-like shapes
        flame_count = 8
        for i in range(flame_count):
            x = (width * i) // flame_count + width // (flame_count * 2)
            base_y = height - 50

            # Flame shape (irregular triangle)
            flame_height = random.randint(height//4, height//2)
            flame_width = random.randint(20, 40)

            color = fire_colors[i % len(fire_colors)]

            # Draw flame as series of lines
            for h in range(0, flame_height, 10):
                y = base_y - h
                width_at_height = int(flame_width * (1 - h / flame_height))

                # Wavy flame edges
                left_x = x - width_at_height + random.randint(-5, 5)
                right_x = x + width_at_height + random.randint(-5, 5)

                cv2.line(frame, (left_x, y), (right_x, y), color, thickness)

        return frame

def test_advanced_grid():
    """Test advanced grid processor"""
    print("🎯 Testing Advanced Grid Processor...")
    
    # Create test frame
    frame = np.zeros((720, 1280, 3), dtype=np.uint8)
    frame[:] = (50, 50, 50)  # Dark gray background
    
    processor = AdvancedGridProcessor()
    
    # Test different grid types
    test_configs = [
        {"type": "random_grid", "color": "white", "opacity": 0.5, "line_width": 2, "anti_reup_intensity": 7},
        {"type": "dynamic_grid", "color": "rainbow", "opacity": 0.4, "line_width": 3, "animated": True},
        {"type": "fibonacci", "color": "blue", "opacity": 0.6, "line_width": 2},
        {"type": "hexagon", "color": "green", "opacity": 0.3, "line_width": 1},
    ]
    
    for i, config in enumerate(test_configs):
        result = processor.apply_advanced_grid(frame.copy(), config, i*10, 100)
        cv2.imwrite(f"test_grid_{i}.jpg", result)
        print(f"✅ Generated test_grid_{i}.jpg")
    
    print("🎉 Advanced grid test completed!")

if __name__ == "__main__":
    test_advanced_grid()
