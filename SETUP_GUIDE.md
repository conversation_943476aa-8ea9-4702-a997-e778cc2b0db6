# 🚀 Enhanced TikTok Bypass Tool - Setup Guide

## 📋 Hướng dẫn Setup sau khi cài Windows mới

### 🎯 **Tổng quan**
Bộ script này giúp bạn nhanh chóng setup lại Enhanced TikTok Bypass Tool sau khi cài Windows mới.

---

## 📦 **Các file script đã tạo:**

### 1. 🔄 **setup_after_windows_install.bat**
- **Mục đích**: Setup hoàn chỉnh từ đầu
- **Chức năng**:
  - Kiểm tra và cài Python 3.11
  - Kiểm tra Git
  - Cài đặt tất cả dependencies
  - Test import và chức năng
  - Tạo log chi tiết

### 2. ⚡ **quick_setup.bat** 
- **Mục đích**: Setup nhanh khi đã có Python
- **Chức năng**:
  - Kiểm tra Python
  - Cài core packages
  - Test nhanh

### 3. 💾 **backup_settings.py**
- **Mục đích**: Backup trước khi cài Windows
- **Chức năng**:
  - Backup tất cả files quan trọng
  - Tạo ZIP archive
  - Tạo script restore
  - Lưu system info

### 4. 🧪 **test_tool.py**
- **Mục đích**: Test toàn bộ chức năng
- **Chức năng**:
  - Test imports
  - Test files tồn tại
  - Test UI creation
  - Tạo báo cáo chi tiết

---

## 🔧 **Hướng dẫn sử dụng:**

### **TRƯỚC KHI CÀI WINDOWS:**

1. **Tạo backup:**
   ```bash
   python backup_settings.py
   ```
   - Sẽ tạo file ZIP backup với timestamp
   - Lưu vào USB hoặc cloud storage

2. **Test tool hiện tại:**
   ```bash
   python test_tool.py
   ```
   - Kiểm tra tất cả chức năng
   - Tạo báo cáo test

### **SAU KHI CÀI WINDOWS MỚI:**

1. **Copy project folder** về máy mới

2. **Chạy setup hoàn chỉnh:**
   ```bash
   # Right-click -> Run as Administrator
   setup_after_windows_install.bat
   ```

3. **Hoặc setup nhanh (nếu đã có Python):**
   ```bash
   quick_setup.bat
   ```

4. **Test tool:**
   ```bash
   python test_tool.py
   ```

5. **Chạy tool:**
   ```bash
   python launch_complete.py
   ```

---

## 📊 **Các tính năng của script:**

### ✅ **setup_after_windows_install.bat:**
- ✅ Kiểm tra quyền Administrator
- ✅ Detect Windows version
- ✅ Auto download & install Python 3.11
- ✅ Check Git installation
- ✅ Install all dependencies
- ✅ Test imports và functionality
- ✅ Tạo log file chi tiết
- ✅ Error handling và retry logic

### ✅ **backup_settings.py:**
- ✅ Backup tất cả files .py quan trọng
- ✅ Backup directories (ffmpeg, _processed, etc.)
- ✅ Tạo ZIP archive với timestamp
- ✅ Tạo restore.bat script
- ✅ Lưu system info và metadata
- ✅ Error handling cho từng file

### ✅ **test_tool.py:**
- ✅ Test import tất cả modules
- ✅ Check file existence và size
- ✅ Test UI creation (không hiển thị)
- ✅ Test video processor
- ✅ Tạo báo cáo chi tiết
- ✅ Success rate calculation

---

## 🎯 **Workflow khuyến nghị:**

```
1. [Trước cài Win] python backup_settings.py
2. [Trước cài Win] python test_tool.py  
3. [Cài Windows mới]
4. [Copy project folder]
5. [Run as Admin] setup_after_windows_install.bat
6. python test_tool.py
7. python launch_complete.py
```

---

## 🔍 **Troubleshooting:**

### **Nếu setup_after_windows_install.bat lỗi:**
1. Chạy `quick_setup.bat`
2. Cài Python manual từ python.org
3. Chạy `pip install -r requirements.txt`

### **Nếu import lỗi:**
1. Chạy `python test_tool.py` để xem lỗi cụ thể
2. Cài từng package: `pip install PyQt5 opencv-python numpy`
3. Check Python version: `python --version`

### **Nếu UI không hiện:**
1. Check `python enhanced_ui_complete.py`
2. Check display settings
3. Try `python launch_complete.py`

---

## 📝 **Log Files:**

- **Setup logs**: `logs/setup_YYYYMMDD_HHMMSS.log`
- **Test reports**: `test_report_YYYYMMDD_HHMMSS.txt`
- **Backup info**: `backup_YYYYMMDD_HHMMSS/backup_info.json`

---

## 🎉 **Kết quả mong đợi:**

Sau khi chạy xong các script:
- ✅ Python 3.11 installed
- ✅ All dependencies installed  
- ✅ Tool tested và working
- ✅ UI hiển thị bình thường
- ✅ Có thể drag & drop video
- ✅ Có thể process video

---

**🚀 Happy coding! Tool sẽ sẵn sàng sử dụng ngay sau khi setup!**
