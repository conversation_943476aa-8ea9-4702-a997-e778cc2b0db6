# ui_config.py - Version với Grid Lines cải tiến
import subprocess
CREATE_NO_WINDOW = 0x08000000
import os
import sys

# Check PyQt5 import
try:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog,
        QCheckBox, QComboBox, QProgressBar, QTextEdit, QGroupBox, QMessageBox,
        QHBoxLayout, QSlider, QSpinBox, QFrame, QListWidget, QListWidgetItem,
        QSplitter, QTabWidget
    )
    from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt, QMimeData, QUrl
    from PyQt5.QtGui import QFont, QColor, QPalette, QDragEnterEvent, QDropEvent
except ImportError:
    print("Cần cài đặt PyQt5: pip install PyQt5")
    sys.exit(1)

# Constants
SUPPORTED_FORMATS = "Video Files (*.mp4 *.mov *.avi *.mkv *.flv *.wmv)"
DEFAULT_ZOOM = "110"
MIN_OPACITY = 10
MAX_OPACITY = 80


from PyQt5.QtCore import pyqtSignal
import time
from datetime import datetime, timedelta

class DragDropFileList(QListWidget):
    """Custom QListWidget with drag & drop support"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.InternalMove)
        self.parent_ui = parent

        # Modern style
        self.setStyleSheet("""
            QListWidget {
                border: 2px dashed #4CAF50;
                border-radius: 10px;
                background-color: #f8f9fa;
                font-size: 13px;
                padding: 15px;
                min-height: 120px;
            }
            QListWidget::item {
                padding: 8px;
                border: 1px solid #e0e0e0;
                background-color: white;
                border-radius: 5px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e8f5e8;
            }
        """)

        # Add placeholder
        self.add_placeholder()

    def add_placeholder(self):
        if self.count() == 0:
            placeholder = QListWidgetItem("🖱️ Kéo thả video vào đây hoặc CLICK để chọn\n📁 Hỗ trợ: MP4, AVI, MOV, MKV, WMV, FLV")
            placeholder.setFlags(Qt.NoItemFlags)
            placeholder.setTextAlignment(Qt.AlignCenter)
            self.addItem(placeholder)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
            self.setStyleSheet(self.styleSheet().replace("#4CAF50", "#2196F3"))
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        self.setStyleSheet(self.styleSheet().replace("#2196F3", "#4CAF50"))

    def dropEvent(self, event):
        files = [u.toLocalFile() for u in event.mimeData().urls()]
        video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'))]

        if video_files and self.parent_ui:
            for file_path in video_files:
                self.parent_ui.add_video_file(file_path)

        self.dragLeaveEvent(event)
        event.accept()

    def add_video_file(self, file_path):
        # Remove placeholder if exists
        if self.count() == 1 and self.item(0).flags() == Qt.NoItemFlags:
            self.clear()

        # Add new file
        file_name = os.path.basename(file_path)
        try:
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            item_text = f"🎬 {file_name}\n📊 {file_size:.1f} MB"
        except:
            item_text = f"🎬 {file_name}"

        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, file_path)
        self.addItem(item)

    def get_all_files(self):
        files = []
        for i in range(self.count()):
            item = self.item(i)
            if item.flags() != Qt.NoItemFlags:  # Skip placeholder
                file_path = item.data(Qt.UserRole)
                if file_path:
                    files.append(file_path)
        return files

    def clear_files(self):
        self.clear()
        self.add_placeholder()

    def mousePressEvent(self, event):
        """Handle click to open file dialog"""
        if event.button() == Qt.LeftButton:
            # Check if clicked on empty area or placeholder
            item = self.itemAt(event.pos())
            if not item or item.flags() == Qt.NoItemFlags:
                # Open file dialog
                if self.parent_ui:
                    self.parent_ui.select_files()
        super().mousePressEvent(event)

class VideoProcessorThread(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, video_paths, effects, zoom_percent, num_threads=2):
        super().__init__()
        self.video_paths = video_paths
        self.effects = effects
        self.zoom_percent = zoom_percent
        self.num_threads = num_threads

    def run(self):
        try:
            from video_processor import process_videos

            def progress_callback(percent):
                self.progress_updated.emit(percent)

            def status_callback(message):
                try:
                    if isinstance(message, bytes):
                        message = message.decode('utf-8', errors='replace')
                    safe_message = str(message)[:500]
                    self.status_updated.emit(safe_message)
                except Exception as e:
                    self.status_updated.emit(f"Status update error: {type(e).__name__}")

            def complete_callback():
                self.finished.emit()

            process_videos(
                self.video_paths,
                self.effects,
                progress_callback,
                complete_callback,
                self.zoom_percent,
                status_callback,
                self.num_threads  # ✅ truyền số luồng
            )

            

        except ImportError as e:
            self.error_occurred.emit(f"Lỗi import video_processor: {str(e)}")
        except Exception as e:
            error_str = str(e).replace('\n', ' ')[:200]
            self.error_occurred.emit(f"Lỗi xử lý: {error_str}")

class UiConfig(QWidget):
    def __init__(self):
        super().__init__()
        self.video_paths = []
        self.processor_thread = None
        self.start_time = None
        self.processed_count = 0
        self.total_count = 0
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("🎬 TikTok Bypass Tool - Enhanced")
        self.setGeometry(50, 50, 1400, 900)  # Much larger default
        self.setMinimumSize(1200, 800)  # Larger minimum

        # Auto maximize for optimal experience
        self.showMaximized()

        # Create scroll area for main content
        from PyQt5.QtWidgets import QScrollArea
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Main widget inside scroll
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(8)  # Reduced spacing

        # Remove title widget - already in window title

        # Zoom moved to bypass effects section

        grid_group = QGroupBox("📏 Grid Lines (Auto-enabled when configured)")
        grid_layout = QVBoxLayout()

        # Grid Type selector (always visible)
        grid_type_layout = QHBoxLayout()
        grid_type_layout.addWidget(QLabel("Grid Type:"))
        self.comboBox_grid_type = QComboBox()
        self.comboBox_grid_type.addItems([
            "None (Disabled)", "3x3 (Rule of Thirds)", "4x4 Grid", "5x5 Grid", "6x6 Grid",
            "Center Cross", "Golden Ratio", "Diagonal Lines",
            "🔥 Random Grid (Anti-Reup)", "🎯 Dynamic Grid (Moving)",
            "⚡ Asymmetric Grid", "🌟 Fibonacci Spiral", "💫 Hexagon Grid",
            "🎨 Custom Pattern", "🔀 Multi-Layer Grid"
        ])
        self.comboBox_grid_type.setCurrentIndex(0)  # Default to "None"
        self.comboBox_grid_type.setToolTip("Chọn grid type (None = tắt grid)")
        self.comboBox_grid_type.currentTextChanged.connect(self.toggle_grid_options)
        grid_type_layout.addWidget(self.comboBox_grid_type)
        grid_layout.addLayout(grid_type_layout)

        # Grid options frame (toggleable)
        self.grid_options_frame = QFrame()
        grid_options_layout = QVBoxLayout()

        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("Màu Grid:"))
        self.comboBox_grid_color = QComboBox()
        self.comboBox_grid_color.addItems([
            "Trắng (White)", "Đỏ (Red)", "Xanh Dương (Blue)", "Xanh Lá (Green)",
            "Vàng (Yellow)", "Tím (Purple)", "Cam (Orange)", "Hồng (Pink)",
            "🌈 Rainbow (Multi-Color)", "🔥 Random Color", "💫 Gradient",
            "👻 Semi-Transparent", "⚡ Flashing Colors"
        ])
        self.comboBox_grid_color.setToolTip("Chọn màu grid dễ nhìn")
        color_layout.addWidget(self.comboBox_grid_color)
        grid_options_layout.addLayout(color_layout)

        opacity_layout = QVBoxLayout()
        opacity_label_layout = QHBoxLayout()
        opacity_label_layout.addWidget(QLabel("Độ trong suốt:"))
        self.label_opacity_value = QLabel("30%")
        opacity_label_layout.addWidget(self.label_opacity_value)
        opacity_layout.addLayout(opacity_label_layout)

        self.slider_opacity = QSlider(Qt.Horizontal)
        self.slider_opacity.setMinimum(MIN_OPACITY)
        self.slider_opacity.setMaximum(MAX_OPACITY)
        self.slider_opacity.setValue(30)
        self.slider_opacity.setTickPosition(QSlider.TicksBelow)
        self.slider_opacity.setTickInterval(10)
        self.slider_opacity.setToolTip("Điều chỉnh độ trong suốt của grid lines")
        self.slider_opacity.valueChanged.connect(self.update_opacity_label)
        opacity_layout.addWidget(self.slider_opacity)
        grid_options_layout.addLayout(opacity_layout)

        width_layout = QHBoxLayout()
        width_layout.addWidget(QLabel("Độ dày line:"))
        self.spinBox_line_width = QSpinBox()
        self.spinBox_line_width.setMinimum(1)
        self.spinBox_line_width.setMaximum(6)
        self.spinBox_line_width.setValue(2)
        self.spinBox_line_width.setSuffix(" px")
        self.spinBox_line_width.setToolTip("Độ dày của đường grid")
        width_layout.addWidget(self.spinBox_line_width)
        width_layout.addStretch()
        grid_options_layout.addLayout(width_layout)

        # Anti-Reup Settings
        anti_reup_layout = QVBoxLayout()

        self.checkbox_random_grid = QCheckBox("🔥 Random Grid Position (Anti-Reup)")
        self.checkbox_random_grid.setToolTip("Thay đổi vị trí grid ngẫu nhiên mỗi frame để bypass detection")
        anti_reup_layout.addWidget(self.checkbox_random_grid)

        self.checkbox_dynamic_opacity = QCheckBox("💫 Dynamic Opacity (Breathing Effect)")
        self.checkbox_dynamic_opacity.setToolTip("Độ trong suốt thay đổi theo thời gian")
        anti_reup_layout.addWidget(self.checkbox_dynamic_opacity)

        self.checkbox_grid_animation = QCheckBox("⚡ Animated Grid (Moving Lines)")
        self.checkbox_grid_animation.setToolTip("Grid lines di chuyển nhẹ để tránh detection")
        anti_reup_layout.addWidget(self.checkbox_grid_animation)

        # Advanced timing
        timing_layout = QHBoxLayout()
        timing_layout.addWidget(QLabel("Grid Timing:"))
        self.comboBox_grid_timing = QComboBox()
        self.comboBox_grid_timing.addItems([
            "Always On", "First 3s Only", "Last 3s Only",
            "Random Intervals", "Fade In/Out", "Pulse Effect"
        ])
        self.comboBox_grid_timing.setToolTip("Khi nào hiển thị grid")
        timing_layout.addWidget(self.comboBox_grid_timing)
        anti_reup_layout.addLayout(timing_layout)

        # Intensity control
        intensity_layout = QHBoxLayout()
        intensity_layout.addWidget(QLabel("Anti-Reup Intensity:"))
        self.slider_anti_reup = QSlider(Qt.Horizontal)
        self.slider_anti_reup.setMinimum(1)
        self.slider_anti_reup.setMaximum(10)
        self.slider_anti_reup.setValue(5)
        self.slider_anti_reup.setToolTip("Mức độ thay đổi để bypass (1=nhẹ, 10=mạnh)")
        self.label_intensity = QLabel("5")
        self.slider_anti_reup.valueChanged.connect(lambda v: self.label_intensity.setText(str(v)))
        intensity_layout.addWidget(self.slider_anti_reup)
        intensity_layout.addWidget(self.label_intensity)
        anti_reup_layout.addLayout(intensity_layout)

        grid_options_layout.addLayout(anti_reup_layout)

        self.btn_preview_grid = QPushButton("👁️ Preview Grid")
        self.btn_preview_grid.clicked.connect(self.preview_grid_settings)
        grid_options_layout.addWidget(self.btn_preview_grid)

        self.grid_options_frame.setLayout(grid_options_layout)
        # Initially show grid options (will be toggled by selection)
        self.grid_options_frame.setVisible(False)
        grid_layout.addWidget(self.grid_options_frame)

        grid_group.setLayout(grid_layout)
        main_layout.addWidget(grid_group)
        # Bypass Effects (compact with zoom)
        effects_group = QGroupBox("🎭 Bypass Effects")
        effects_layout = QVBoxLayout()

        # Zoom control (moved here)
        zoom_layout = QHBoxLayout()
        zoom_layout.addWidget(QLabel("🔍 Zoom:"))
        self.comboBox_zoom = QComboBox()
        self.comboBox_zoom.addItems(["100%", "105%", "110%", "115%", "120%", "125%", "130%"])
        self.comboBox_zoom.setCurrentText(DEFAULT_ZOOM + "%")
        self.comboBox_zoom.setToolTip("Mức zoom để bypass detection")
        zoom_layout.addWidget(self.comboBox_zoom)
        effects_layout.addLayout(zoom_layout)

        self.checkbox_shift = QCheckBox("🔄 Shift khung hình")
        self.checkbox_blur = QCheckBox("🌫️ Blur viền")
        self.checkbox_audio = QCheckBox("🎵 Chỉnh âm thanh")
        self.checkbox_color_shift = QCheckBox("🎨 Color shift")
        self.checkbox_noise = QCheckBox("📺 Thêm nhiễu")
        self.checkbox_vibration = QCheckBox("📳 Vibration")
        self.checkbox_overlay = QCheckBox("🖼️ Overlay")
        self.checkbox_blackframe = QCheckBox("⚫ Frame đen")
        self.checkbox_gpu = QCheckBox("🚀 Dùng GPU (experimental)")
        self.checkbox_speed = QCheckBox("⚡ Ưu tiên tốc độ (p1 / CRF 25)")
        self.checkbox_speed.setToolTip("Tăng tốc độ xử lý tối đa bằng cách giảm nhẹ chất lượng encode")

        # 🎬 VIDEO ENHANCEMENT - INTEGRATED WITH OTHER EFFECTS
        self.checkbox_enhance = QCheckBox("✨ Video Enhancement")
        self.checkbox_enhance.setToolTip("Làm sắc nét video + cải thiện màu sắc")
        self.checkbox_enhance.setChecked(False)
        self.checkbox_enhance.toggled.connect(self.toggle_enhance_options)

        # Enhancement method selection - INLINE
        self.comboBox_enhance_method = QComboBox()
        self.comboBox_enhance_method.addItems([
            "⚡ Ultra Fast",
            "🤖 High Quality"
        ])
        self.comboBox_enhance_method.setCurrentText("⚡ Ultra Fast")
        self.comboBox_enhance_method.setVisible(False)  # Hidden by default
        self.label_threads = QLabel("🔁 Số video xử lý cùng lúc:")
        self.spin_threads = QSpinBox()
        self.spin_threads.setMinimum(1)
        self.spin_threads.setMaximum(4)
        self.spin_threads.setValue(2)
        self.spin_threads.setToolTip("Tùy chỉnh số video xử lý song song. Quá cao có thể gây quá tải.")

        self.checkbox_shift.setToolTip("Dịch chuyển nhẹ khung hình")
        self.checkbox_blur.setToolTip("Làm mờ viền video")
        self.checkbox_audio.setToolTip("Điều chỉnh audio để bypass")
        self.checkbox_color_shift.setToolTip("Thay đổi màu sắc nhẹ")
        self.checkbox_noise.setToolTip("Thêm nhiễu nhỏ")
        self.checkbox_gpu.setToolTip("Sử dụng GPU để xử lý nhanh hơn")

        effects_row1 = QHBoxLayout()
        effects_col1 = QVBoxLayout()
        effects_col2 = QVBoxLayout()
        effects_col2.addWidget(self.checkbox_speed)

        
        effects_col1.addWidget(self.checkbox_shift)
        effects_col1.addWidget(self.checkbox_blur)
        effects_col1.addWidget(self.checkbox_audio)
        effects_col1.addWidget(self.checkbox_color_shift)
        effects_col1.addWidget(self.checkbox_noise)

        effects_col2.addWidget(self.checkbox_vibration)
        effects_col2.addWidget(self.checkbox_overlay)
        effects_col2.addWidget(self.checkbox_blackframe)
        effects_col2.addWidget(self.checkbox_gpu)

        # Add enhancement to col2
        effects_col2.addWidget(self.checkbox_enhance)
        effects_col2.addWidget(self.comboBox_enhance_method)


        
        effects_row1.addLayout(effects_col1)
        effects_row1.addLayout(effects_col2)
        effects_layout.addLayout(effects_row1)
            
        effects_group.setLayout(effects_layout)
        main_layout.addWidget(effects_group)

        # File selection with drag & drop
        file_group = QGroupBox("📁 Video Files (Drag & Drop)")
        file_layout = QVBoxLayout()

        # Create drag & drop list
        self.file_list = DragDropFileList(self)
        file_layout.addWidget(self.file_list)

        # Clear button only (select is integrated into drag-drop area)
        btn_layout = QHBoxLayout()
        self.btn_clear = QPushButton("🗑️ Xóa tất cả")
        self.btn_clear.clicked.connect(self.clear_files)
        self.btn_clear.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)

        btn_layout.addWidget(self.btn_clear)
        btn_layout.addStretch()  # Push button to left
        file_layout.addLayout(btn_layout)

        self.label_selected = QLabel("🖱️ Kéo thả video vào khung trên hoặc click vào khung để chọn files")
        self.label_selected.setWordWrap(True)
        self.label_selected.setMaximumHeight(60)
        self.label_selected.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.label_selected)

        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)
        effects_layout.addWidget(self.label_threads)
        effects_layout.addWidget(self.spin_threads)




        # Process button
        self.btn_start = QPushButton("🎬 Bắt đầu xử lý")
        self.btn_start.clicked.connect(self.start_processing)
        self.btn_start.setEnabled(False)
        self.btn_start.setMinimumHeight(45)
        
        self.btn_start.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        main_layout.addWidget(self.btn_start)

        # ✅ THÊM NÚT XEM TRƯỚC HIỆU ỨNG
        self.btn_preview_effects = QPushButton("👁️ Xem trước hiệu ứng")
        self.btn_preview_effects.setMinimumHeight(35)
        self.btn_preview_effects.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.btn_preview_effects.clicked.connect(self.preview_effects)
        main_layout.addWidget(self.btn_preview_effects)

        # Progress with ETA
        progress_group = QGroupBox("📊 Tiến độ & ETA")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 6px;
            }
        """)

        self.label_status = QLabel("⏳ Sẵn sàng...")
        self.label_status.setWordWrap(True)
        self.label_status.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")

        # ETA and speed labels
        self.label_eta = QLabel("")
        self.label_eta.setStyleSheet("font-size: 12px; color: #666;")

        self.label_speed = QLabel("")
        self.label_speed.setStyleSheet("font-size: 12px; color: #666;")

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)  # Much larger minimum
        self.log_text.setMaximumHeight(400)  # Allow expansion
        self.log_text.setVisible(True)  # Always visible for debugging
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-family: 'Courier New', monospace;
                font-size: 13px;
                color: #333;
                line-height: 1.4;
            }
        """)

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.label_status)
        progress_layout.addWidget(self.label_eta)
        progress_layout.addWidget(self.label_speed)
        progress_layout.addWidget(self.log_text)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # Set scroll area as main layout
        scroll.setWidget(main_widget)

        # Final layout
        final_layout = QVBoxLayout()
        final_layout.addWidget(scroll)
        self.setLayout(final_layout)

        # Initialize grid options visibility
        self.toggle_grid_options(self.comboBox_grid_type.currentText())

        # FORCE RESET ALL CHECKBOXES TO PREVENT AUTO-UPSCALE
        self.reset_all_checkboxes()
    def reset_all_checkboxes(self):
        """FORCE RESET all checkboxes to prevent auto-upscale"""
        print("🔧 FORCE RESETTING all checkboxes to False...")

        # Reset all effect checkboxes
        self.checkbox_shift.setChecked(False)
        self.checkbox_blur.setChecked(False)
        self.checkbox_audio.setChecked(False)
        self.checkbox_color_shift.setChecked(False)
        self.checkbox_noise.setChecked(False)
        self.checkbox_vibration.setChecked(False)
        self.checkbox_overlay.setChecked(False)
        self.checkbox_blackframe.setChecked(False)
        self.checkbox_gpu.setChecked(False)
        self.checkbox_speed.setChecked(False)

        # Reset enhancement checkbox
        self.checkbox_enhance.setChecked(False)
        print("✅ ENHANCEMENT CHECKBOX RESET TO FALSE")

        # Reset grid to None
        self.comboBox_grid_type.setCurrentIndex(0)  # "None (Disabled)"

        print("✅ All checkboxes reset to default (False)")

    def toggle_grid_options(self, text):
        """Toggle grid options based on selection"""
        is_enabled = text != "None (Disabled)"
        self.grid_options_frame.setVisible(is_enabled)

    def toggle_enhance_options(self, checked):
        self.comboBox_enhance_method.setVisible(checked)

    def update_opacity_label(self, value):
        self.label_opacity_value.setText(f"{value}%")

    def preview_grid_settings(self):
        grid_type_map = {
            0: "3x3",
            1: "4x4", 
            2: "5x5",
            3: "6x6",
            4: "center_cross",
            5: "golden_ratio",
            6: "diagonal"
        }

        color_map = {
            0: "white",
            1: "red",
            2: "blue", 
            3: "green",
            4: "yellow",
            5: "purple",
            6: "orange"
        }

        grid_type = grid_type_map.get(self.comboBox_grid_type.currentIndex(), "3x3")
        color = color_map.get(self.comboBox_grid_color.currentIndex(), "white")
        opacity = self.slider_opacity.value()
        line_width = self.spinBox_line_width.value()

        preview_text = f"""📏 Grid Preview Settings:

🔸 Loại: {self.comboBox_grid_type.currentText()}
🔸 Màu: {self.comboBox_grid_color.currentText()}  
🔸 Độ trong suốt: {opacity}%
🔸 Độ dày: {line_width}px

✅ Settings này sẽ được áp dụng cho video!"""

        QMessageBox.information(self, "Grid Preview", preview_text)

    def get_grid_config(self):
        # Check if grid is disabled
        if self.comboBox_grid_type.currentText() == "None (Disabled)":
            return {}

        grid_type_map = {
            0: "none", 1: "3x3", 2: "4x4", 3: "5x5", 4: "6x6",
            5: "center_cross", 6: "golden_ratio", 7: "diagonal",
            8: "random_grid", 9: "dynamic_grid", 10: "asymmetric",
            11: "fibonacci", 12: "hexagon", 13: "custom_pattern", 14: "multi_layer"
        }

        color_map = {
            0: "white", 1: "red", 2: "blue", 3: "green",
            4: "yellow", 5: "purple", 6: "orange", 7: "pink",
            8: "rainbow", 9: "random_color", 10: "gradient",
            11: "semi_transparent", 12: "flashing"
        }

        timing_map = {
            0: "always", 1: "first_3s", 2: "last_3s",
            3: "random_intervals", 4: "fade_inout", 5: "pulse"
        }

        opacity = max(0.1, min(0.8, self.slider_opacity.value() / 100.0))
        line_width = max(1, min(6, self.spinBox_line_width.value()))

        return {
            "type": grid_type_map.get(self.comboBox_grid_type.currentIndex(), "3x3"),
            "color": color_map.get(self.comboBox_grid_color.currentIndex(), "white"),
            "opacity": opacity,
            "line_width": line_width,
            # Anti-Reup features
            "random_position": getattr(self, 'checkbox_random_grid', None) and self.checkbox_random_grid.isChecked(),
            "dynamic_opacity": getattr(self, 'checkbox_dynamic_opacity', None) and self.checkbox_dynamic_opacity.isChecked(),
            "animated": getattr(self, 'checkbox_grid_animation', None) and self.checkbox_grid_animation.isChecked(),
            "timing": timing_map.get(getattr(self, 'comboBox_grid_timing', None) and self.comboBox_grid_timing.currentIndex(), "always"),
            "anti_reup_intensity": getattr(self, 'slider_anti_reup', None) and self.slider_anti_reup.value() or 5
        }

    def validate_video_files(self, files):
        valid_files = []
        invalid_files = []
        
        for file_path in files:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                if file_path.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv')):
                    valid_files.append(file_path)
                else:
                    invalid_files.append(file_path)
            else:
                invalid_files.append(file_path)
        
        return valid_files, invalid_files

    def select_files(self):
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self, 
                "Chọn video files", 
                "", 
                SUPPORTED_FORMATS
            )
            
            if files:
                valid_files, invalid_files = self.validate_video_files(files)
                
                if invalid_files:
                    QMessageBox.warning(
                        self, "Cảnh báo", 
                        f"Có {len(invalid_files)} file không hợp lệ sẽ bị bỏ qua:\n" + 
                        "\n".join([os.path.basename(f) for f in invalid_files[:3]])
                    )
                
                if valid_files:
                    self.video_paths = [os.path.normpath(f) for f in valid_files]

                    file_names = []
                    for f in valid_files:
                        try:
                            name = os.path.basename(f)
                            if isinstance(name, bytes):
                                name = name.decode('utf-8', errors='replace')
                            file_names.append(name)
                        except:
                            file_names.append("(file with encoding issue)")

                    display_text = f"✅ Đã chọn {len(valid_files)} file:\n"
                    display_text += "\n".join(file_names[:3])
                    if len(valid_files) > 3:
                        display_text += f"\n... và {len(valid_files)-3} file khác"

                    self.label_selected.setText(display_text)
                    self.btn_start.setEnabled(True)
                else:
                    self.label_selected.setText("Không có file hợp lệ nào được chọn")
                    self.btn_start.setEnabled(False)
                    self.video_paths = []
            else:
                self.label_selected.setText("Chưa chọn file nào")
                self.btn_start.setEnabled(False)
                self.video_paths = []
                
        except Exception as e:
            error_msg = str(e).replace('\n', ' ')[:100]
            QMessageBox.warning(self, "Lỗi", f"Không thể chọn file: {error_msg}")
            self.video_paths = []
            self.btn_start.setEnabled(False)

    def add_video_file(self, file_path):
        """Add video file from drag & drop"""
        if file_path not in self.video_paths:
            self.video_paths.append(file_path)
            self.file_list.add_video_file(file_path)
            self.update_file_display()

    def clear_files(self):
        """Clear all files"""
        self.video_paths = []
        self.file_list.clear_files()
        self.update_file_display()

    def update_file_display(self):
        """Update file display label"""
        if self.video_paths:
            file_names = [os.path.basename(f) for f in self.video_paths]
            display_text = f"✅ Đã chọn {len(self.video_paths)} file:\n"
            display_text += "\n".join(file_names[:3])
            if len(self.video_paths) > 3:
                display_text += f"\n... và {len(self.video_paths)-3} file khác"

            self.label_selected.setText(display_text)
            self.btn_start.setEnabled(True)
        else:
            self.label_selected.setText("🖱️ Kéo thả video vào khung trên hoặc click vào khung để chọn files")
            self.btn_start.setEnabled(False)

    def update_eta(self, progress_percent):
        """Update ETA calculation"""
        if self.start_time and progress_percent > 0:
            elapsed = time.time() - self.start_time

            # Calculate ETA
            total_estimated = (elapsed / progress_percent) * 100
            remaining = total_estimated - elapsed

            if remaining > 0:
                eta_time = datetime.now() + timedelta(seconds=remaining)
                eta_str = eta_time.strftime("%H:%M:%S")

                # Format remaining time
                if remaining < 60:
                    remaining_str = f"{remaining:.0f}s"
                elif remaining < 3600:
                    remaining_str = f"{remaining/60:.1f}m"
                else:
                    remaining_str = f"{remaining/3600:.1f}h"

                self.label_eta.setText(f"⏰ ETA: {eta_str} (còn {remaining_str})")

                # Speed calculation
                speed = progress_percent / elapsed if elapsed > 0 else 0
                self.label_speed.setText(f"🚀 Tốc độ: {speed:.1f}%/s")

    def get_selected_effects(self):
        effects = {
            "zoom": True,
            "shift": self.checkbox_shift.isChecked(),
            "blur": self.checkbox_blur.isChecked(),
            "audio": self.checkbox_audio.isChecked(),
            "guides": self.comboBox_grid_type.currentText() != "None (Disabled)",
            "color_shift": self.checkbox_color_shift.isChecked(),
            "noise": self.checkbox_noise.isChecked(),
            "vibration": self.checkbox_vibration.isChecked(),
            "overlay": self.checkbox_overlay.isChecked(),
            "blackframe": self.checkbox_blackframe.isChecked(),
            "gpu": self.checkbox_gpu.isChecked(),
            "enhance": self.checkbox_enhance.isChecked()
        }

        # Check if grid is enabled (not "None (Disabled)")
        if self.comboBox_grid_type.currentText() != "None (Disabled)":
            effects["grid_config"] = self.get_grid_config()

        # Enhancement settings
        if self.checkbox_enhance.isChecked():
            # Parse method from combo text
            method_text = self.comboBox_enhance_method.currentText()
            if "AI-Based" in method_text or "High Quality" in method_text:
                effects["enhancement_method"] = "ai_sharp"
            else:
                effects["enhancement_method"] = "multi_pass"

        return effects

    def start_processing(self):
        if not self.video_paths:
            QMessageBox.warning(self, "Cảnh báo", "Chưa chọn video nào!")
            return

        # Initialize ETA tracking
        self.start_time = time.time()
        self.processed_count = 0
        self.total_count = len(self.video_paths)
        self.label_eta.setText("")
        self.label_speed.setText("")

        try:
            import video_processor
        except ImportError:
            QMessageBox.critical(
                self, "Lỗi", 
                "Không tìm thấy file video_processor.py!\nVui lòng đảm bảo file video_processor.py nằm cùng thư mục."
            )
            return
            
        self.btn_start.setEnabled(False)
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.setVisible(True)
        self.log_text.clear()
        
        effects = self.get_selected_effects()
        zoom_percent = int(self.comboBox_zoom.currentText().replace("%", ""))
        
        if effects.get("guides"):
            grid_info = effects.get("grid_config", {})
            self.log_text.append(f"🔧 Grid Settings: {grid_info.get('type', 'N/A')} - {grid_info.get('color', 'N/A')}")
        
        num_threads = self.spin_threads.value()
        self.processor_thread = VideoProcessorThread(self.video_paths, effects, zoom_percent, num_threads)
        self.processor_thread.progress_updated.connect(self.update_progress)
        self.processor_thread.status_updated.connect(self.update_status)
        self.processor_thread.finished.connect(self.processing_finished)
        self.processor_thread.error_occurred.connect(self.processing_error)
        
        self.processor_thread.start()
        self.label_status.setText("🔄 Đang xử lý video...")

    def update_progress(self, value):
        try:
            self.progress_bar.setValue(max(0, min(100, value)))
            self.update_eta(value)  # Update ETA

            if value == 100:
                # Show completion time
                if self.start_time:
                    total_time = time.time() - self.start_time
                    if total_time < 60:
                        time_str = f"{total_time:.1f}s"
                    elif total_time < 3600:
                        time_str = f"{total_time/60:.1f}m"
                    else:
                        time_str = f"{total_time/3600:.1f}h"

                    self.label_eta.setText(f"✅ Hoàn thành trong {time_str}")
                    self.label_speed.setText("")
        except Exception as e:
            print(f"Progress update error: {e}")

    def update_status(self, message):
        try:
            if not isinstance(message, str):
                message = str(message)
            
            self.label_status.setText(message)
            self.log_text.append(message)
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            print(f"Status update error: {e}")
            self.label_status.setText("Status update error")

    def processing_finished(self):
        try:
            self.label_status.setText("✅ Hoàn thành! Kiểm tra thư mục 'output_videos'")
            self.progress_bar.setValue(100)
            
            self.btn_start.setEnabled(True)
            
            if self.processor_thread:
                self.processor_thread.deleteLater()
                self.processor_thread = None
            
            QMessageBox.information(
                self, "Thành công", 
                "🎉 Đã xử lý xong tất cả video!\n\n✅ Kiểm tra thư mục 'output_videos' để xem kết quả.\n\n📏 Grid lines đã được áp dụng theo setting!"
            )
        except Exception as e:
            print(f"Finish handling error: {e}")

    def processing_error(self, error_msg):
        try:
            self.label_status.setText(f"❌ Lỗi: {error_msg}")
            
            self.btn_start.setEnabled(True)
            
            if self.processor_thread:
                self.processor_thread.deleteLater()
                self.processor_thread = None
            
            QMessageBox.critical(self, "Lỗi", f"Có lỗi xảy ra:\n\n{error_msg}")
        except Exception as e:
            print(f"Error handling error: {e}")

    def preview_effects(self):
        if not self.video_paths:
            QMessageBox.warning(self, "Cảnh báo", "Bạn cần chọn ít nhất 1 video để xem trước.")
            return

        input_path = self.video_paths[0]
        effects = self.get_selected_effects()
        zoom_percent = int(self.comboBox_zoom.currentText().replace("%", ""))

        try:
            from video_processor import VideoProcessor
            import subprocess
            import platform
            import shutil
            import os

            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                creationflags = 0x08000000 | 0x00000008  # CREATE_NO_WINDOW | CREATE_NEW_CONSOLE

            processor = VideoProcessor()
            temp_dir = os.path.join("temp_preview")
            output_path = os.path.join(temp_dir, "preview.mp4")
            temp_cut = os.path.join(temp_dir, "cut_preview.mp4")

            shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)

            cut_cmd = [
                processor.ffmpeg_manager.get_path(),
                "-y", "-i", input_path,
                "-t", "10", "-c", "copy", temp_cut
            ]
            subprocess.run(cut_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
               creationflags=subprocess.CREATE_NO_WINDOW)

            cmd = processor.build_ffmpeg_command(temp_cut, output_path, effects, zoom_percent)
            subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                           creationflags=subprocess.CREATE_NO_WINDOW)

            if platform.system() == "Windows":
                os.startfile(output_path)
            elif platform.system() == "Darwin":
                subprocess.run(["open", output_path])
            else:
                subprocess.run(["xdg-open", output_path])

        except Exception as e:
            QMessageBox.critical(self, "Lỗi Preview", f"Không thể xem trước: {str(e)}")

    def closeEvent(self, event):
        try:
            if self.processor_thread and self.processor_thread.isRunning():
                reply = QMessageBox.question(
                    self, "Xác nhận", 
                    "Đang xử lý video. Bạn có chắc muốn thoát không?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    self.processor_thread.requestInterruption()
                    self.processor_thread.quit()
                    self.processor_thread.wait(5000)
                    if self.processor_thread.isRunning():
                        self.processor_thread.terminate()
                        self.processor_thread.wait(2000)
                    event.accept()
                else:
                    event.ignore()
            else:
                event.accept()
        except Exception as e:
            print(f"Close event error: {e}")
            event.accept()
