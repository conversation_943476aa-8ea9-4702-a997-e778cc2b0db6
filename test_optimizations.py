#!/usr/bin/env python3
"""
🚀 Test script for video processing optimizations
Tests the enhanced Wink-style enhancement and performance improvements
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QThread, pyqtSignal

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from video_processor import VideoProcessor
from enhanced_ui_complete import EnhancedCompleteUI

class OptimizationTester(QThread):
    """Thread for testing optimizations without blocking UI"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(dict)
    
    def __init__(self, test_video_path=None):
        super().__init__()
        self.test_video_path = test_video_path
        
    def run(self):
        """Run optimization tests"""
        results = {}
        
        try:
            self.progress.emit("🔍 Initializing video processor...")
            processor = VideoProcessor()
            
            # Test 1: System capabilities detection
            self.progress.emit("🖥️ Testing system capabilities detection...")
            encoder, gpu_type = processor.get_optimal_encoder()
            optimal_threads = processor.get_optimal_threads()
            
            results['system'] = {
                'encoder': encoder,
                'gpu_type': gpu_type,
                'optimal_threads': optimal_threads
            }
            
            self.progress.emit(f"✅ System: {gpu_type.upper()} encoder, {optimal_threads} threads")
            
            # Test 2: Wink enhancement filters
            self.progress.emit("🌟 Testing Wink enhancement filters...")
            
            wink_filters = {}
            for level in ['light', 'medium', 'high', 'ultra']:
                filters = processor.get_wink_enhancement_filters(level)
                wink_filters[level] = filters
                self.progress.emit(f"   {level}: {len(filters)} filters")
            
            results['wink_filters'] = wink_filters
            
            # Test 3: FFmpeg command building
            self.progress.emit("⚙️ Testing FFmpeg command optimization...")
            
            test_effects = {
                'wink_enhancement': True,
                'wink_level': 'high',
                'processing_priority': 'balanced',
                'fast_decode': True,
                'memory_optimize': True,
                'use_gpu': gpu_type != 'cpu'
            }
            
            if self.test_video_path and os.path.exists(self.test_video_path):
                cmd = processor.build_ffmpeg_command(
                    self.test_video_path, 
                    "test_output.mp4", 
                    test_effects
                )
                results['ffmpeg_cmd'] = cmd
                self.progress.emit(f"✅ FFmpeg command: {len(cmd)} parameters")
            else:
                self.progress.emit("⚠️ No test video provided, skipping FFmpeg test")
                results['ffmpeg_cmd'] = None
            
            # Test 4: Performance estimation
            self.progress.emit("📊 Calculating performance estimates...")
            
            # Estimate processing speed based on system
            base_speed = 1.0  # 1x realtime baseline
            
            if gpu_type == "nvidia":
                speed_multiplier = 3.0
            elif gpu_type == "amd":
                speed_multiplier = 2.5
            elif gpu_type == "intel":
                speed_multiplier = 2.0
            else:
                speed_multiplier = 1.0
            
            # Thread bonus
            if optimal_threads >= 8:
                speed_multiplier *= 1.5
            elif optimal_threads >= 4:
                speed_multiplier *= 1.2
            
            estimated_speed = base_speed * speed_multiplier
            results['performance'] = {
                'estimated_speed': estimated_speed,
                'speed_description': self.get_speed_description(estimated_speed)
            }
            
            self.progress.emit(f"🚀 Estimated speed: {estimated_speed:.1f}x realtime")
            
            # Test 5: Memory optimization
            self.progress.emit("🧠 Testing memory optimization...")
            
            import psutil
            memory_info = psutil.virtual_memory()
            
            # Calculate optimal buffer sizes
            available_gb = memory_info.available / (1024**3)
            
            if available_gb >= 8:
                buffer_size = "large"
                concurrent_videos = min(optimal_threads, 4)
            elif available_gb >= 4:
                buffer_size = "medium"
                concurrent_videos = min(optimal_threads, 2)
            else:
                buffer_size = "small"
                concurrent_videos = 1
            
            results['memory'] = {
                'available_gb': available_gb,
                'buffer_size': buffer_size,
                'concurrent_videos': concurrent_videos
            }
            
            self.progress.emit(f"💾 Memory: {available_gb:.1f}GB available, {buffer_size} buffers")
            
            self.progress.emit("✅ All optimization tests completed!")
            
        except Exception as e:
            self.progress.emit(f"❌ Test error: {str(e)}")
            results['error'] = str(e)
        
        self.finished.emit(results)
    
    def get_speed_description(self, speed):
        """Get human-readable speed description"""
        if speed >= 4.0:
            return "🔥 Cực nhanh"
        elif speed >= 2.5:
            return "🚀 Rất nhanh"
        elif speed >= 1.5:
            return "⚡ Nhanh"
        elif speed >= 1.0:
            return "💻 Bình thường"
        else:
            return "🐌 Chậm"

def test_ui_optimizations():
    """Test UI optimizations"""
    print("🎨 Testing UI optimizations...")
    
    app = QApplication(sys.argv)
    
    # Create UI instance
    ui = EnhancedCompleteUI()
    
    # Test system detection
    system_info = ui.detect_system_capabilities()
    print(f"✅ System detection: {system_info}")
    
    # Test UI components
    print("🔧 Testing UI components...")
    
    # Check if new components exist
    components_to_check = [
        'combo_wink_level',
        'combo_priority', 
        'checkbox_fast_decode',
        'checkbox_memory_optimize',
        'checkbox_parallel_processing'
    ]
    
    missing_components = []
    for component in components_to_check:
        if not hasattr(ui, component):
            missing_components.append(component)
    
    if missing_components:
        print(f"⚠️ Missing UI components: {missing_components}")
    else:
        print("✅ All UI components present")
    
    return ui, system_info

def main():
    """Main test function"""
    print("🚀 Starting optimization tests...")
    print("=" * 50)
    
    # Test 1: UI optimizations
    try:
        ui, system_info = test_ui_optimizations()
        print(f"✅ UI test passed")
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        return
    
    # Test 2: Video processor optimizations
    print("\n🎬 Testing video processor optimizations...")
    
    # Look for test video
    test_video = None
    possible_paths = [
        "test_video.mp4",
        "sample.mp4",
        "video.mp4"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            test_video = path
            break
    
    if test_video:
        print(f"📹 Using test video: {test_video}")
    else:
        print("⚠️ No test video found, running limited tests")
    
    # Create and run optimization tester
    tester = OptimizationTester(test_video)
    
    # Connect signals for console output
    tester.progress.connect(lambda msg: print(f"   {msg}"))
    
    # Run tests
    tester.run()  # Run synchronously for testing
    
    print("\n" + "=" * 50)
    print("🎉 Optimization tests completed!")
    print("\n📋 Summary of improvements:")
    print("   🌟 Multi-level Wink enhancement (Light/Medium/High/Ultra)")
    print("   🚀 Automatic GPU detection and optimization")
    print("   ⚡ Intelligent thread management")
    print("   🧠 Memory optimization")
    print("   🎯 Processing priority modes")
    print("   📊 Performance scoring")
    print("   🔧 Hardware-specific encoding")

if __name__ == "__main__":
    main()
